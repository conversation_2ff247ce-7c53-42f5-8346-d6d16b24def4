package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class ChunkDatabaseMigrationServiceTest {

    @Mock
    private S3FileUploadService s3FileUploadService;

    @Mock
    private DSLContext duckDsl;

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private DbActivityService activityLogService;

    @Mock
    private InteractionService interactionService;

    @InjectMocks
    private ChunkDatabaseMigrationService chunkDatabaseMigrationService;

    private static final String TEST_STUDY_ID = "study123";
    private static final String TEST_TEMP_FILE_LOCATION = "/tmp/";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(chunkDatabaseMigrationService, "tempFileLocation", TEST_TEMP_FILE_LOCATION);
    }

    @Test
    void testCreateSQLiteDSL_Success() throws Exception {
        String testFilePath = "/tmp/test.db";

        try (MockedStatic<DriverManager> driverManagerMock = Mockito.mockStatic(DriverManager.class)) {
            Connection mockConnection = mock(Connection.class);
            driverManagerMock.when(() -> DriverManager.getConnection("jdbc:sqlite:" + testFilePath))
                    .thenReturn(mockConnection);

            Pair<DSLContext, Connection> result = chunkDatabaseMigrationService.createSQLiteDSL(testFilePath);

            assertNotNull(result);
            assertNotNull(result.getLeft());
            assertNotNull(result.getRight());
            assertEquals(mockConnection, result.getRight());
        }
    }

    @Test
    void testCreateSQLiteDSL_ThrowsException() {
        String invalidFilePath = "/invalid/path/test.db";

        try (MockedStatic<DriverManager> driverManagerMock = Mockito.mockStatic(DriverManager.class)) {
            driverManagerMock.when(() -> DriverManager.getConnection(anyString()))
                    .thenThrow(new RuntimeException("Connection failed"));

            assertThrows(RuntimeException.class, () -> {
                chunkDatabaseMigrationService.createSQLiteDSL(invalidFilePath);
            });
        }
    }

    @Test
    void testIsCompletedDataExtraction_AllCompleted() {
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID,
                ActionType.STUDY_METADATA_MIGRATION))
                        .thenReturn(true);
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID, ActionType.PARTICIPANT_MIGRATION))
                .thenReturn(true);
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID, ActionType.CGM_MIGRATION))
                .thenReturn(true);
        when(interactionService.isMealsOrFitnessInteractionExist(TEST_STUDY_ID))
                .thenReturn(true);

        Map<String, Object> result = chunkDatabaseMigrationService.isCompletedDataExtraction(TEST_STUDY_ID);

        assertNotNull(result);
        assertEquals(true, result.get("studyMetaData"));
        assertEquals(true, result.get("participant"));
        assertEquals(true, result.get("cgm"));
        assertEquals(true, result.get("mealsAndFitnes")); // Note: typo in original code

        verify(interactionService).isDbFileInteractionFinishedForAction(TEST_STUDY_ID,
                ActionType.STUDY_METADATA_MIGRATION);
        verify(interactionService).isDbFileInteractionFinishedForAction(TEST_STUDY_ID,
                ActionType.PARTICIPANT_MIGRATION);
        verify(interactionService).isDbFileInteractionFinishedForAction(TEST_STUDY_ID, ActionType.CGM_MIGRATION);
        verify(interactionService).isMealsOrFitnessInteractionExist(TEST_STUDY_ID);
    }

    @Test
    void testIsCompletedDataExtraction_PartiallyCompleted() {
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID,
                ActionType.STUDY_METADATA_MIGRATION))
                        .thenReturn(true);
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID, ActionType.PARTICIPANT_MIGRATION))
                .thenReturn(false);
        when(interactionService.isDbFileInteractionFinishedForAction(TEST_STUDY_ID, ActionType.CGM_MIGRATION))
                .thenReturn(true);
        when(interactionService.isMealsOrFitnessInteractionExist(TEST_STUDY_ID))
                .thenReturn(false);

        Map<String, Object> result = chunkDatabaseMigrationService.isCompletedDataExtraction(TEST_STUDY_ID);

        assertNotNull(result);
        assertEquals(true, result.get("studyMetaData"));
        assertEquals(false, result.get("participant"));
        assertEquals(true, result.get("cgm"));
        assertEquals(false, result.get("mealsAndFitnes")); // Note: typo in original code
    }

    @Test
    void testStartExtraction_Success() {
        String saveDBInteractionId = "interaction123";
        String dbFileId = "file456";

        when(interactionService.getSuccessDbFileInteractionIdOfActionType(ActionType.SAVE_DB_CONTENT, TEST_STUDY_ID))
                .thenReturn(saveDBInteractionId);
        when(interactionService.getDbFileIdOfCompletedCgmRowData(TEST_STUDY_ID))
                .thenReturn(dbFileId);

        lenient().when(interactionService.isDbFileInteractionFinishedForAction(eq(TEST_STUDY_ID), anyString()))
                .thenReturn(true);
        JSONB mockLastInteractionResponse = JSONB.valueOf(
                "{\"hub_interaction_id\": \"hub123\", \"request\": \"test\", \"db_file_id\": \"file456\", \"file_content_type\": \"application/octet-stream\", \"file_location\": \"/tmp/test.db\", \"file_name\": \"test.db\", \"file_processing_initiated_at\": \"2023-01-01T00:00:00Z\", \"interaction_hierarchy\": \"[]\", \"study_id\": \"study123\"}");
        lenient().when(interactionService.getLastInteractionLog(anyString())).thenReturn(mockLastInteractionResponse);
        lenient()
                .when(interactionService.getAndSetInteractionHierarchyFromInteractionLog(any(JSONB.class), anyString()))
                .thenReturn(List.of());

        String result = chunkDatabaseMigrationService.startExtraction(TEST_STUDY_ID);

        assertNotNull(result);
        verify(interactionService, Mockito.atLeast(1))
                .getSuccessDbFileInteractionIdOfActionType(ActionType.SAVE_DB_CONTENT, TEST_STUDY_ID);
        verify(interactionService).getDbFileIdOfCompletedCgmRowData(TEST_STUDY_ID);
    }

    @Test
    void testStartExtraction_Exception_ReturnsFailure() {
        when(interactionService.getSuccessDbFileInteractionIdOfActionType(ActionType.SAVE_DB_CONTENT, TEST_STUDY_ID))
                .thenThrow(new RuntimeException("Database error"))
                .thenReturn("lastInteractionId");

        JSONB mockLastInteractionResponse = JSONB.valueOf(
                "{\"hub_interaction_id\": \"hub123\", \"request\": \"test\", \"db_file_id\": \"file456\", \"file_content_type\": \"application/octet-stream\", \"file_location\": \"/tmp/test.db\", \"file_name\": \"test.db\", \"file_processing_initiated_at\": \"2023-01-01T00:00:00Z\", \"interaction_hierarchy\": \"[]\", \"study_id\": \"study123\"}");
        when(interactionService.getLastInteractionLog("lastInteractionId")).thenReturn(mockLastInteractionResponse);
        when(interactionService.getAndSetInteractionHierarchyFromInteractionLog(any(JSONB.class), anyString()))
                .thenReturn(List.of());

        String result = chunkDatabaseMigrationService.startExtraction(TEST_STUDY_ID);

        assertEquals("Extraction Failed", result);
    }

    @Test
    void testMealsAndFitnessDataExists_IndirectTest() throws Exception {
        String studyId = "testStudy";
        String dbFileId = "testDbFile";

        when(interactionService.getDbFileIdOfCompletedCgmRowData(studyId)).thenReturn(dbFileId);

        try {
            Method mealsAndFitnessDataExistsMethod = ChunkDatabaseMigrationService.class
                    .getDeclaredMethod("mealsAndFitnessDataExists", String.class);
            mealsAndFitnessDataExistsMethod.setAccessible(true);

            try {
                mealsAndFitnessDataExistsMethod.invoke(chunkDatabaseMigrationService, studyId);
            } catch (Exception e) {

            }

            verify(interactionService).getDbFileIdOfCompletedCgmRowData(studyId);
        } catch (NoSuchMethodException e) {
            fail("Method mealsAndFitnessDataExists should exist");
        }
    }

    @Test
    void testSetHierarchyArray_Success() throws Exception {
        Method setHierarchyArrayMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("setHierarchyArray", List.class, JSONB.class);
        setHierarchyArrayMethod.setAccessible(true);

        List<String> hierarchyJsonArray = new ArrayList<>(Arrays.asList("interaction1", "interaction2"));
        JSONB subFileInteractionResponse = JSONB.valueOf("{\"file_interaction_id\": \"interaction3\"}");

        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) setHierarchyArrayMethod.invoke(chunkDatabaseMigrationService,
                hierarchyJsonArray, subFileInteractionResponse);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("interaction1", result.get(0));
        assertEquals("interaction2", result.get(1));
        assertEquals("interaction3", result.get(2));
    }

    @Test
    void testSetHierarchyArray_ImmutableList() throws Exception {
        Method setHierarchyArrayMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("setHierarchyArray", List.class, JSONB.class);
        setHierarchyArrayMethod.setAccessible(true);

        List<String> hierarchyJsonArray = List.of("interaction1");
        JSONB subFileInteractionResponse = JSONB.valueOf("{\"file_interaction_id\": \"interaction2\"}");

        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) setHierarchyArrayMethod.invoke(chunkDatabaseMigrationService,
                hierarchyJsonArray, subFileInteractionResponse);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("interaction1", result.get(0));
        assertEquals("interaction2", result.get(1));
        assertTrue(result instanceof ArrayList);
    }

    @Test
    void testAttachSqliteDatabase_MethodAccessibility() throws Exception {
        Method attachSqliteDatabaseMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("attachSqliteDatabase", String.class, String.class);
        attachSqliteDatabaseMethod.setAccessible(true);

        String tempFilePath = "/tmp/test.db";
        String sqliteDbName = "test_sqlite_db";

        try {
            attachSqliteDatabaseMethod.invoke(chunkDatabaseMigrationService, tempFilePath, sqliteDbName);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException ||
                    e.getCause() instanceof NullPointerException ||
                    e.getCause() instanceof IllegalArgumentException);
        }

        assertNotNull(attachSqliteDatabaseMethod);
        assertTrue(attachSqliteDatabaseMethod.canAccess(chunkDatabaseMigrationService));
    }

    @Test
    void testPrepareJson_Success() throws Exception {
        Method prepareJsonMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("prepareJson", String.class, String.class, String.class, String.class,
                        long.class, String.class, String.class, String.class);
        prepareJsonMethod.setAccessible(true);

        String fileId = "file123";
        String fileName = "test.db";
        String fileURL = "https://s3.amazonaws.com/test.db";
        String uploadTimestamp = "2023-01-01T00:00:00Z";
        long fileSize = 1024L;
        String studyId = "study123";
        String userPartyId = "user456";
        String organizationPartyId = "org789";

        String result = (String) prepareJsonMethod.invoke(chunkDatabaseMigrationService,
                fileId, fileName, fileURL, uploadTimestamp, fileSize, studyId, userPartyId, organizationPartyId);

        assertNotNull(result);
        assertTrue(result.contains("\"db_file_id\":\"" + fileId + "\""));
        assertTrue(result.contains("\"file_name\":\"" + fileName + "\""));
        assertTrue(result.contains("\"file_url\":\"" + fileURL + "\""));
        assertTrue(result.contains("\"upload_timestamp\":\"" + uploadTimestamp + "\""));
        assertTrue(result.contains("\"file_size\":" + fileSize));
        assertTrue(result.contains("\"study_id\":\"" + studyId + "\""));
        assertTrue(result.contains("\"uploaded_by\":\"" + userPartyId + "\""));
        assertTrue(result.contains("\"org_party_id\":\"" + organizationPartyId + "\""));
        assertTrue(result.contains("\"current_user_id\":\"" + userPartyId + "\""));
    }

    @Test
    void testCopyTablesFromSqLiteToPostgres_MethodAccessibility() throws Exception {
        Method copyTablesMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("copyTablesFromSqLiteToPostgres", String.class, String.class, String.class,
                        org.jooq.JSONB.class, DSLContext.class, String.class);
        copyTablesMethod.setAccessible(true);

        String studyId = "study123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "file456";
        org.jooq.JSONB dbData = org.jooq.JSONB.valueOf("{}");
        DSLContext sqliteDsl = mock(DSLContext.class);
        String sqliteDbName = "test_sqlite_db";

        try {
            copyTablesMethod.invoke(chunkDatabaseMigrationService,
                    studyId, filePath, distinctDbFileIds, dbData, sqliteDsl, sqliteDbName);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException ||
                    e.getCause() instanceof NullPointerException ||
                    e.getCause() instanceof IllegalArgumentException);
        }

        assertNotNull(copyTablesMethod);
        assertTrue(copyTablesMethod.canAccess(chunkDatabaseMigrationService));
    }

    @Test
    void testExtractAndSaveInteraction_Success() throws Exception {
        Method extractAndSaveInteractionMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("extractAndSaveInteraction", String.class);
        extractAndSaveInteractionMethod.setAccessible(true);

        String lastInteractionId = "interaction123";

        JSONB mockLastInteractionResponse = JSONB.valueOf(
                "{\"hub_interaction_id\": \"hub123\", \"request\": \"test\", \"db_file_id\": \"file456\", \"file_content_type\": \"application/octet-stream\", \"file_location\": \"/tmp/test.db\", \"file_name\": \"test.db\", \"file_processing_initiated_at\": \"2023-01-01T00:00:00Z\", \"interaction_hierarchy\": \"[]\", \"study_id\": \"study123\"}");
        when(interactionService.getLastInteractionLog(lastInteractionId)).thenReturn(mockLastInteractionResponse);
        when(interactionService.getAndSetInteractionHierarchyFromInteractionLog(any(JSONB.class), anyString()))
                .thenReturn(Arrays.asList("interaction1", "interaction2"));

        extractAndSaveInteractionMethod.invoke(chunkDatabaseMigrationService, lastInteractionId);

        verify(interactionService).getLastInteractionLog(lastInteractionId);
        verify(interactionService).getAndSetInteractionHierarchyFromInteractionLog(any(JSONB.class), anyString());
    }

    @Test
    void testCleanUpDatabase_MethodAccessibilityAndExecution() throws Exception {
        Method cleanUpDatabaseMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("cleanUpDatabase", String.class, String.class);
        cleanUpDatabaseMethod.setAccessible(true);

        String dbFileId = "file123";
        String lastInteractionId = "interaction456";

        try {
            cleanUpDatabaseMethod.invoke(chunkDatabaseMigrationService, dbFileId, lastInteractionId);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException ||
                    e.getCause() instanceof NullPointerException ||
                    e.getCause() instanceof IllegalArgumentException);
        }

        assertNotNull(cleanUpDatabaseMethod);
        assertTrue(cleanUpDatabaseMethod.canAccess(chunkDatabaseMigrationService));
    }

    @Test
    void testCleanUpDatabase_MethodAccessibility() throws Exception {
        Method cleanUpDatabaseMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("cleanUpDatabase", String.class, String.class);
        cleanUpDatabaseMethod.setAccessible(true);

        String dbFileId = "file123";
        String lastInteractionId = "interaction456";

        try {
            cleanUpDatabaseMethod.invoke(chunkDatabaseMigrationService, dbFileId, lastInteractionId);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException ||
                    e.getCause() instanceof NullPointerException ||
                    e.getCause() instanceof IllegalArgumentException);
        }

        assertNotNull(cleanUpDatabaseMethod);
        assertTrue(cleanUpDatabaseMethod.canAccess(chunkDatabaseMigrationService));
    }

    @Test
    void testCleanUpDatabase_ParameterValidation() throws Exception {
        Method cleanUpDatabaseMethod = ChunkDatabaseMigrationService.class
                .getDeclaredMethod("cleanUpDatabase", String.class, String.class);
        cleanUpDatabaseMethod.setAccessible(true);

        String validDbFileId = "valid_file_123";
        String validInteractionId = "valid_interaction_456";

        try {
            cleanUpDatabaseMethod.invoke(chunkDatabaseMigrationService, validDbFileId, validInteractionId);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException ||
                    e.getCause() instanceof NullPointerException);
        }

        Class<?>[] parameterTypes = cleanUpDatabaseMethod.getParameterTypes();
        assertEquals(2, parameterTypes.length);
        assertEquals(String.class, parameterTypes[0]);
        assertEquals(String.class, parameterTypes[1]);
    }

}
