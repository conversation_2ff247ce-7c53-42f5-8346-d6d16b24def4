package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;

import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
public class DatabaseMigrationServiceTest {

    @Mock
    private S3FileUploadService s3FileUploadService;

    @Mock
    private DSLContext duckDsl;

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private MultipartFile mockFile;

    @Mock
    private Record1<String> mockRecord;

    private DatabaseMigrationService databaseMigrationService;

    private DatabaseMigrationRequest testRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testRequest = new DatabaseMigrationRequest("STUDY123", "ORG456");

        // Create the service manually with mocked dependencies
        databaseMigrationService = new DatabaseMigrationService(
                s3FileUploadService,
                duckDsl,
                dsl,
                userNameService,
                partyService);
    }

    @Test
    void testInit_Success() {
        // Test the @PostConstruct init method - just verify it doesn't throw exceptions
        assertDoesNotThrow(() -> databaseMigrationService.init());
    }

    @Test
    void testConstructor_Success() {
        // Test that the service can be constructed with all dependencies
        DatabaseMigrationService service = new DatabaseMigrationService(
                s3FileUploadService, duckDsl, dsl, userNameService, partyService);
        assertNotNull(service);
    }

    @Test
    void testUploadAndSaveDBFile_FileUploadFails() throws IOException {
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Failed to upload file to S3") ||
                exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testUploadAndSaveDBFile_ExceptionHandling() throws IOException {
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest))
                .thenThrow(new RuntimeException("S3 upload failed"));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUploadAndSaveDBFile_DatabaseAlreadyExists() throws Exception {
        // Given
        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        // Mock DSL to simulate database already exists (exists method returns true when
        // count > 0)
        org.jooq.SelectSelectStep<org.jooq.Record1<Integer>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<Integer>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<Integer>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectOne()).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(anyString(), any())).thenReturn(whereStep);
        lenient().when(dsl.fetchExists(whereStep)).thenReturn(true); // Simulate record exists

        // When
        String result = databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest);

        // Then
        assertEquals("Database already exists for study: " + testRequest.studyId(), result);
    }

    @Test
    void testUploadAndSaveDBFile_SaveFileToTempLocationFails() throws Exception {
        // Given
        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(null);

        // Mock DSL to simulate database doesn't exist (exists method returns false when
        // count = 0)
        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false); // Simulate no record exists

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertTrue(exception.getMessage().contains("Failed to save file to temporary location"));
    }

    @Test
    void testUploadAndSaveDBFile_DuckDslException() throws Exception {
        // Given
        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";

        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        // Mock DSL to simulate database doesn't exist
        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        // Mock DuckDB DSL to throw exception when trying to get distinct db_file_id
        lenient().when(duckDsl.fetchOne(anyString())).thenThrow(new RuntimeException("DuckDB connection failed"));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testUploadAndSaveDBFile_UserServiceException() throws Exception {
        // Given
        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";
        String distinctDbFileId = "FILE123";

        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(mockFile.getSize()).thenReturn(1024L);
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        // Mock DSL to simulate database doesn't exist
        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        // Mock DuckDB DSL for getting distinct db_file_id
        @SuppressWarnings("unchecked")
        Record1<String> dbFileIdRecord = mock(Record1.class);
        lenient().when(dbFileIdRecord.get("db_file_id", String.class)).thenReturn(distinctDbFileId);
        lenient().when(duckDsl.fetchOne(anyString())).thenReturn(dbFileIdRecord);

        // Mock user services to throw exception
        lenient().when(userNameService.getUserId()).thenThrow(new RuntimeException("User service failed"));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testCreateSQLiteDSL_Exception() throws Exception {
        // Test that the method handles invalid paths properly
        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.createSQLiteDSL("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    void testValidateRequiredColumns_Exception() throws Exception {
        // Test that the method handles exceptions properly
        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.validateRequiredColumns("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testValidateRequiredColumns_AllColumnsPresent() throws Exception {
        // Mock DSLContext for SQLite
        DSLContext mockSqliteDsl = mock(DSLContext.class);

        // Mock result for file_meta_ingest_data table
        Result<Record> fileMetaResult = mock(Result.class);
        Record fileMetaRecord1 = mock(Record.class);
        Record fileMetaRecord2 = mock(Record.class);
        Record fileMetaRecord3 = mock(Record.class);
        Record fileMetaRecord4 = mock(Record.class);
        Record fileMetaRecord5 = mock(Record.class);

        when(fileMetaRecord1.get("name", String.class)).thenReturn("file_meta_id");
        when(fileMetaRecord2.get("name", String.class)).thenReturn("db_file_id");
        when(fileMetaRecord3.get("name", String.class)).thenReturn("participant_display_id");
        when(fileMetaRecord4.get("name", String.class)).thenReturn("file_meta_data");
        when(fileMetaRecord5.get("name", String.class)).thenReturn("cgm_data");

        when(fileMetaResult.iterator()).thenReturn(
                List.of(fileMetaRecord1, fileMetaRecord2, fileMetaRecord3, fileMetaRecord4, fileMetaRecord5)
                        .iterator());

        // Mock result for participant table
        Result<Record> participantResult = mock(Result.class);
        Record participantRecord1 = mock(Record.class);
        Record participantRecord2 = mock(Record.class);
        Record participantRecord3 = mock(Record.class);
        Record participantRecord4 = mock(Record.class);
        Record participantRecord5 = mock(Record.class);
        Record participantRecord6 = mock(Record.class);
        Record participantRecord7 = mock(Record.class);
        Record participantRecord8 = mock(Record.class);
        Record participantRecord9 = mock(Record.class);
        Record participantRecord10 = mock(Record.class);
        Record participantRecord11 = mock(Record.class);
        Record participantRecord12 = mock(Record.class);
        Record participantRecord13 = mock(Record.class);
        Record participantRecord14 = mock(Record.class);
        Record participantRecord15 = mock(Record.class);

        when(participantRecord1.get("name", String.class)).thenReturn("db_file_id");
        when(participantRecord2.get("name", String.class)).thenReturn("tenant_id");
        when(participantRecord3.get("name", String.class)).thenReturn("study_display_id");
        when(participantRecord4.get("name", String.class)).thenReturn("participant_display_id");
        when(participantRecord5.get("name", String.class)).thenReturn("site_id");
        when(participantRecord6.get("name", String.class)).thenReturn("diagnosis_icd");
        when(participantRecord7.get("name", String.class)).thenReturn("med_rxnorm");
        when(participantRecord8.get("name", String.class)).thenReturn("treatment_modality");
        when(participantRecord9.get("name", String.class)).thenReturn("gender");
        when(participantRecord10.get("name", String.class)).thenReturn("race_ethnicity");
        when(participantRecord11.get("name", String.class)).thenReturn("age");
        when(participantRecord12.get("name", String.class)).thenReturn("bmi");
        when(participantRecord13.get("name", String.class)).thenReturn("baseline_hba1c");
        when(participantRecord14.get("name", String.class)).thenReturn("diabetes_type");
        when(participantRecord15.get("name", String.class)).thenReturn("study_arm");

        when(participantResult.iterator()).thenReturn(
                List.of(participantRecord1, participantRecord2, participantRecord3, participantRecord4,
                        participantRecord5, participantRecord6, participantRecord7, participantRecord8,
                        participantRecord9, participantRecord10, participantRecord11, participantRecord12,
                        participantRecord13, participantRecord14, participantRecord15).iterator());

        // Mock the fetch calls for PRAGMA table_info
        when(mockSqliteDsl.fetch("PRAGMA table_info(file_meta_ingest_data)")).thenReturn(fileMetaResult);
        when(mockSqliteDsl.fetch("PRAGMA table_info(participant)")).thenReturn(participantResult);

        // Use reflection to test the method with mocked DSL
        try {
            Method createSQLiteDSLMethod = DatabaseMigrationService.class.getDeclaredMethod("createSQLiteDSL",
                    String.class);
            createSQLiteDSLMethod.setAccessible(true);

            // Create a spy to override the createSQLiteDSL method
            DatabaseMigrationService spyService = spy(databaseMigrationService);
            doReturn(mockSqliteDsl).when(spyService).createSQLiteDSL(anyString());

            boolean result = spyService.validateRequiredColumns("/valid/path/test.db");
            assertTrue(result);

        } catch (Exception e) {
            fail("Failed to test validateRequiredColumns with all columns present: " + e.getMessage());
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    void testValidateRequiredColumns_MissingColumns() throws Exception {
        // Mock DSLContext for SQLite
        DSLContext mockSqliteDsl = mock(DSLContext.class);

        // Mock result for file_meta_ingest_data table with missing columns
        Result<Record> fileMetaResult = mock(Result.class);
        Record fileMetaRecord1 = mock(Record.class);
        Record fileMetaRecord2 = mock(Record.class);
        Record fileMetaRecord3 = mock(Record.class);

        when(fileMetaRecord1.get("name", String.class)).thenReturn("file_meta_id");
        when(fileMetaRecord2.get("name", String.class)).thenReturn("db_file_id");
        when(fileMetaRecord3.get("name", String.class)).thenReturn("participant_display_id");
        // Missing: file_meta_data, cgm_data

        when(fileMetaResult.iterator()).thenReturn(
                List.of(fileMetaRecord1, fileMetaRecord2, fileMetaRecord3).iterator());

        // Mock the fetch calls for PRAGMA table_info
        when(mockSqliteDsl.fetch("PRAGMA table_info(file_meta_ingest_data)")).thenReturn(fileMetaResult);

        // Since the method validates both tables, we need to mock the participant table
        // too
        // But since file_meta_ingest_data fails first, the method should return false
        // before checking participant
        // However, let's mock it to be safe using lenient stubbing
        Result<Record> participantResult = mock(Result.class);
        lenient().when(participantResult.iterator()).thenReturn(Collections.emptyIterator());
        lenient().when(mockSqliteDsl.fetch("PRAGMA table_info(participant)")).thenReturn(participantResult);

        // Use reflection to test the method with mocked DSL
        try {
            Method createSQLiteDSLMethod = DatabaseMigrationService.class.getDeclaredMethod("createSQLiteDSL",
                    String.class);
            createSQLiteDSLMethod.setAccessible(true);

            // Create a spy to override the createSQLiteDSL method
            DatabaseMigrationService spyService = spy(databaseMigrationService);
            doReturn(mockSqliteDsl).when(spyService).createSQLiteDSL(anyString());

            boolean result = spyService.validateRequiredColumns("/valid/path/test.db");
            assertFalse(result);

        } catch (Exception e) {
            fail("Failed to test validateRequiredColumns with missing columns: " + e.getMessage());
        }
    }

    @Test
    void testValidateParticipantData_Exception() throws Exception {
        // Test that the method handles exceptions properly
        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.validateParticipantData("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    void testPrepareJson_Success() {
        try {
            Method prepareJsonMethod = DatabaseMigrationService.class.getDeclaredMethod("prepareJson",
                    String.class, String.class, String.class, String.class, long.class,
                    String.class, String.class, String.class);
            prepareJsonMethod.setAccessible(true);

            String result = (String) prepareJsonMethod.invoke(databaseMigrationService,
                    "FILE123", "test", "https://s3.amazonaws.com/test.db", "2024-01-01T00:00:00Z",
                    1024L, "STUDY123", "PARTY123", "ORG456");

            assertNotNull(result);
            assertTrue(result.contains("FILE123"));
            assertTrue(result.contains("test"));
            assertTrue(result.contains("STUDY123"));
        } catch (Exception e) {
            fail("Failed to test prepareJson method: " + e.getMessage());
        }
    }

    @Test
    void testExists_ReturnsTrue() {
        try {
            Method existsMethod = DatabaseMigrationService.class.getDeclaredMethod("exists", String.class);
            existsMethod.setAccessible(true);

            // Test that the method exists and can be called (will fail due to null DSL, but
            // that's expected)
            assertNotNull(existsMethod);
            assertEquals("exists", existsMethod.getName());
            assertEquals(1, existsMethod.getParameterCount());
            assertEquals(String.class, existsMethod.getParameterTypes()[0]);
        } catch (Exception e) {
            fail("Failed to test exists method: " + e.getMessage());
        }
    }

    @Test
    void testExists_ReturnsFalse() {
        try {
            Method existsMethod = DatabaseMigrationService.class.getDeclaredMethod("exists", String.class);
            existsMethod.setAccessible(true);

            // Test that the method has correct signature and return type
            assertEquals(boolean.class, existsMethod.getReturnType());
            assertTrue(existsMethod.canAccess(databaseMigrationService));
        } catch (Exception e) {
            fail("Failed to test exists method: " + e.getMessage());
        }
    }

    @Test
    void testDetachSqliteDatabase_Success() {
        try {
            Method detachSqliteMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
            detachSqliteMethod.setAccessible(true);

            // Test that the method exists and is accessible
            assertNotNull(detachSqliteMethod);
            assertEquals("detachSqliteDatabase", detachSqliteMethod.getName());
            assertEquals(0, detachSqliteMethod.getParameterCount());
            assertEquals(void.class, detachSqliteMethod.getReturnType());
        } catch (Exception e) {
            fail("Failed to test detachSqliteDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testDetachPostgresDatabase_Success() {
        try {
            // Mock the DSL context behavior
            when(duckDsl.execute(anyString())).thenReturn(0);
            @SuppressWarnings("unchecked")
            List<String> mockDatabases = mock(List.class);
            when(mockDatabases.contains(anyString())).thenReturn(true);
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            when(mockResult.getValues(anyInt(), eq(String.class))).thenReturn(mockDatabases);
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method detachPostgresMethod = DatabaseMigrationService.class.getDeclaredMethod("detachPostgresDatabase");
            detachPostgresMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    detachPostgresMethod.invoke(databaseMigrationService);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            fail("Failed to test detachPostgresDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testAttachPostgresDatabase_Success() {
        try {
            // Mock the DSL context behavior
            when(duckDsl.execute(anyString())).thenReturn(0);

            Method attachPostgresMethod = DatabaseMigrationService.class.getDeclaredMethod("attachPostgresDatabase");
            attachPostgresMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    attachPostgresMethod.invoke(databaseMigrationService);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            verify(duckDsl, times(2)).execute(anyString());
        } catch (Exception e) {
            fail("Failed to test attachPostgresDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testAttachSqliteDatabase_Success() {
        try {
            // Mock the DSL context behavior
            when(duckDsl.execute(anyString())).thenReturn(0);

            Method attachSqliteMethod = DatabaseMigrationService.class.getDeclaredMethod("attachSqliteDatabase",
                    String.class);
            attachSqliteMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    attachSqliteMethod.invoke(databaseMigrationService, "/tmp/test.db");
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            verify(duckDsl, times(2)).execute(anyString());
        } catch (Exception e) {
            fail("Failed to test attachSqliteDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testIsDatabaseAttached_ReturnsTrue() {
        try {
            // Mock the DSL context behavior
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            Record mockRecord = mock(Record.class);
            when(mockRecord.get("name")).thenReturn("sqlite_study_db");
            when(mockResult.iterator()).thenReturn(List.of(mockRecord).iterator());
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method isDatabaseAttachedMethod = DatabaseMigrationService.class.getDeclaredMethod("isDatabaseAttached",
                    String.class);
            isDatabaseAttachedMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            boolean result = (boolean) isDatabaseAttachedMethod.invoke(databaseMigrationService, "sqlite_study_db");
            assertTrue(result);
        } catch (Exception e) {
            fail("Failed to test isDatabaseAttached method: " + e.getMessage());
        }
    }

    @Test
    void testIsDatabaseAttached_ReturnsFalse() {
        try {
            // Mock the DSL context behavior
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            Record mockRecord = mock(Record.class);
            when(mockRecord.get("name")).thenReturn("other_db");
            when(mockResult.iterator()).thenReturn(List.of(mockRecord).iterator());
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method isDatabaseAttachedMethod = DatabaseMigrationService.class.getDeclaredMethod("isDatabaseAttached",
                    String.class);
            isDatabaseAttachedMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            boolean result = (boolean) isDatabaseAttachedMethod.invoke(databaseMigrationService, "sqlite_study_db");
            assertFalse(result);
        } catch (Exception e) {
            fail("Failed to test isDatabaseAttached method: " + e.getMessage());
        }
    }

    @Test
    void testCopyTablesFromSqLiteToPostgres_Exception() {
        try {
            Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                    String.class, String.class, String.class, JSONB.class);
            copyTablesMethod.setAccessible(true);

            // Test with invalid parameters to trigger exception
            Exception exception = assertThrows(Exception.class, () -> {
                try {
                    copyTablesMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                } catch (java.lang.reflect.InvocationTargetException e) {
                    throw e.getCause();
                }
            });

            assertNotNull(exception);
        } catch (Exception e) {
            fail("Failed to test copyTablesFromSqLiteToPostgres method: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_Exception() {
        try {
            Method migrateDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDatabaseMethod.setAccessible(true);

            // Test that the method can be invoked (it returns a CompletableFuture)
            assertDoesNotThrow(() -> {
                try {
                    Object result = migrateDatabaseMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                    assertNotNull(result);
                } catch (Exception e) {
                    // Expected to fail due to invalid path, but method should be accessible
                }
            });
        } catch (Exception e) {
            fail("Failed to test migrateDdatabase method: " + e.getMessage());
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_DatabaseAttached() throws Exception {
        // Given
        Result<Record> attachedDatabases = mock(Result.class);
        Record mockDbRecord = mock(Record.class);

        // Mock the database list query
        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(List.of(mockDbRecord).iterator());
        lenient().when(mockDbRecord.get("name")).thenReturn("sqlite_study_db");

        // Use reflection to test the private method
        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        // When
        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        // Then
        verify(duckDsl).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_DatabaseNotAttached() throws Exception {
        // Given
        Result<Record> attachedDatabases = mock(Result.class);
        Record mockDbRecord = mock(Record.class);

        // Mock the database list query to return different database name
        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(List.of(mockDbRecord).iterator());
        lenient().when(mockDbRecord.get("name")).thenReturn("other_db");

        // Use reflection to test the private method
        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        // When
        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        // Then
        verify(duckDsl, never()).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_EmptyDatabaseList() throws Exception {
        // Given
        Result<Record> attachedDatabases = mock(Result.class);

        // Mock empty database list
        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(Collections.emptyIterator());

        // Use reflection to test the private method
        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        // When
        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        // Then
        verify(duckDsl, never()).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_StudyDisplayIdMismatch() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        // Mock SQLite study display ID query
        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SQLITE_STUDY_ID");

        // Mock PostgreSQL study display ID query
        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("POSTGRES_STUDY_ID");

        // Use reflection to test the private method
        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        // When
        String result = (String) copyTablesMethod.invoke(databaseMigrationService, studyId, filePath, distinctDbFileIds,
                dbData);

        // Then
        assertEquals("Study display id mismatch between SQLite and Postgres", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_ValidateRequiredColumnsFails() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        // Mock SQLite study display ID query
        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        // Mock PostgreSQL study display ID query
        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        // Mock validateRequiredColumns to return false
        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(false).when(spyService).validateRequiredColumns(filePath);

        // Use reflection to test the private method
        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        // When
        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        // Then
        assertEquals("The Sqlite Tables do not contains all the required fields", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_ValidateParticipantDataFails() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        // Mock SQLite study display ID query
        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        // Mock PostgreSQL study display ID query
        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        // Mock validateRequiredColumns to return true, validateParticipantData to
        // return false
        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(true).when(spyService).validateRequiredColumns(filePath);
        doReturn(false).when(spyService).validateParticipantData(filePath);

        // Use reflection to test the private method
        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        // When
        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        // Then
        assertEquals("The Sqlite Table for Participant do not contains data for all the required fields", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_Success() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        // Mock SQLite study display ID query
        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        // Mock PostgreSQL study display ID query
        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        // Mock validation methods to return true
        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(true).when(spyService).validateRequiredColumns(filePath);
        doReturn(true).when(spyService).validateParticipantData(filePath);

        // Use reflection to test the private method
        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        // When
        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        // Then
        assertEquals("Proceed with Database Migration", result);
    }

    @Test
    void testCopyTablesFromSqLiteToPostgres_DatabaseException() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        // Mock SQLite study display ID query to throw exception
        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Use reflection to test the private method
        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            copyTablesMethod.invoke(databaseMigrationService, studyId, filePath, distinctDbFileIds, dbData);
        });

        assertTrue(exception.getCause().getMessage().contains("Failed to copy tables from SQLite to Postgres"));
    }

    @Test
    void testMigrateDdatabase_MethodAccessible() throws Exception {
        // Test that the migrateDdatabase method can be accessed via reflection
        // This is a basic test to ensure the method exists and is accessible
        try {
            Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDdatabaseMethod.setAccessible(true);

            // Verify method exists and is accessible
            assertNotNull(migrateDdatabaseMethod);
            assertEquals("migrateDdatabase", migrateDdatabaseMethod.getName());
            assertEquals(4, migrateDdatabaseMethod.getParameterCount());

            // Test that the method can be invoked (it returns a CompletableFuture)
            assertDoesNotThrow(() -> {
                try {
                    Object result = migrateDdatabaseMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                    assertNotNull(result);
                    assertTrue(result instanceof CompletableFuture);
                } catch (Exception e) {
                    // Expected to fail due to invalid path and mocked dependencies, but method
                    // should be accessible
                }
            });
        } catch (Exception e) {
            fail("Failed to test migrateDdatabase method: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_AsyncBehavior() throws Exception {
        // Test that the migrateDdatabase method returns a CompletableFuture and
        // executes asynchronously
        try {
            Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDdatabaseMethod.setAccessible(true);

            // Test that the method returns a CompletableFuture
            Object result = migrateDdatabaseMethod.invoke(databaseMigrationService,
                    "STUDY123", "/tmp/test.db", "FILE123", JSONB.valueOf("{}"));

            assertNotNull(result);
            assertTrue(result instanceof CompletableFuture);

            @SuppressWarnings("unchecked")
            CompletableFuture<String> future = (CompletableFuture<String>) result;

            // The future should complete (either successfully or exceptionally)
            // We don't care about the exact result since we're testing with mocked
            // dependencies
            assertDoesNotThrow(() -> {
                try {
                    future.get(); // This may throw due to mocked dependencies, which is expected
                } catch (Exception e) {
                    // Expected due to mocked dependencies
                }
            });

        } catch (Exception e) {
            fail("Failed to test migrateDdatabase async behavior: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_DuckDbException() throws Exception {
        // Given
        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{\"test\": \"data\"}");

        // Mock DuckDB to throw exception during execute
        lenient().when(duckDsl.execute(anyString())).thenThrow(new RuntimeException("DuckDB connection failed"));

        // Use reflection to test the private method
        Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                String.class, String.class, String.class, JSONB.class);
        migrateDdatabaseMethod.setAccessible(true);

        // When & Then
        @SuppressWarnings("unchecked")
        CompletableFuture<String> result = (CompletableFuture<String>) migrateDdatabaseMethod.invoke(
                databaseMigrationService, studyId, filePath, distinctDbFileIds, dbData);

        // The CompletableFuture should complete exceptionally
        Exception exception = assertThrows(Exception.class, () -> {
            result.get(); // This should throw the exception
        });

        assertTrue(exception.getCause().getMessage().contains("DuckDB connection failed"));
    }
}
