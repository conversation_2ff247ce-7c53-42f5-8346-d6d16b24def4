package org.diabetestechnology.drh.service.http.pg;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileProcessingStatus;
import org.diabetestechnology.drh.service.http.pg.constant.ResearchStudyState;
import org.diabetestechnology.drh.service.http.pg.constant.StudyInteractionType;
import org.diabetestechnology.drh.service.http.pg.request.PublicationUpdateRequest;
import org.diabetestechnology.drh.service.http.pg.request.ResearchStudySettingsRequest;
import org.diabetestechnology.drh.service.http.pg.request.StudyRequest;
import org.diabetestechnology.drh.service.http.pg.service.DbActivityService;
import org.diabetestechnology.drh.service.http.pg.service.InteractionService;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.ParticipantService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.jooq.DSLContext;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.SelectConditionStep;
import org.jooq.SelectField;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.TableLike;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;

import org.jooq.Field;
import org.jooq.JSONB;

import org.jooq.Condition;

public class ResearchStudyServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;
    @Mock
    private DbActivityService activityLogService;
    @Mock
    private MasterService masterService;
    @Mock
    private AuditService auditService;

    @Mock
    private InteractionService interactionService;

    @Mock
    private ResultQuery<Record1<String>> queryMock;
    @Mock
    private ParticipantService participantService;
    @Spy
    @InjectMocks
    private ResearchStudyService researchStudyService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this); // Initialize mocks

        // Initialize common mocks
        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveResearchStudy() throws Exception {
        // Arrange
        StudyRequest request = new StudyRequest(
                "RS127",
                "Heart Study",
                "Study about heart disease",
                "Tenant5",
                "UserABC",
                1);

        String validJsonResponse = "{\"study_id\": \"study-456\"}";
        String studyId = "study-456";
        String interactionResponse = "interaction-456";

        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(String.class)).thenReturn(validJsonResponse);
        when(interactionService.saveHubInteraction(studyId)).thenReturn(interactionResponse);

        String result = researchStudyService.saveResearchStudy(request);

        assertEquals(validJsonResponse, result);
        verify(interactionService).saveHubInteraction(studyId);

        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveResearchStudy_missingStudyIdKey() throws Exception {
        StudyRequest request = new StudyRequest(
                "RS124",
                "Cancer Study",
                "A research study on cancer.",
                "Tenant2",
                "User456",
                1);

        String responseWithoutStudyId = "{\"status\": \"ok\"}";

        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(String.class)).thenReturn(responseWithoutStudyId);
        when(interactionService.saveHubInteraction(null)).thenReturn("interaction-xyz");

        String result = researchStudyService.saveResearchStudy(request);

        assertEquals(responseWithoutStudyId, result);
        verify(interactionService).saveHubInteraction(null);
        verify(interactionService).saveStudyInteraction(
                isNull(), eq("interaction-xyz"), eq(StudyInteractionType.CREATE),
                eq("Failed to Create a Study"), isNull(), isNull(),
                eq(JsonUtils.toJson(request)), isNull(),
                eq(JsonUtils.toJson(new ObjectMapper().readTree(responseWithoutStudyId))),
                eq(0), eq("Failed"), eq(ActionType.CREATE_STUDY), eq(FileProcessingStatus.FAILED));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testEditResearchStudy() throws JsonProcessingException {
        String studyId = "RS123";
        JSONB studyData = JSONB.valueOf("{\"title\": \"New Study Title\",\"description\": \"Study description\"}");
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "HUB789";

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(any())).thenReturn(userPartyId);
        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);

        JSONB expectedResult = JSONB.valueOf("{\"status\": \"success\"}");
        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB actualResult = researchStudyService.editResearchStudy(studyId, studyData);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        assertEquals(expectedResult, actualResult, "The actual result does not match the expected result.");

        verify(dsl, times(1)).select(any(Field.class));
        verify(userNameService, times(1)).getUserId();
        verify(partyService, times(1)).getPartyIdByUserId(userId);

    }

    @Test
    void testEditResearchStudy_whenExceptionThrown() throws JsonProcessingException {
        String studyId = "RS123";
        JSONB studyData = JSONB.valueOf("{\"title\": \"New Study Title\"}");
        String hubInteractionId = "HUB789";

        when(userNameService.getUserId()).thenThrow(new RuntimeException("Simulated exception"));
        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);

        JSONB result = researchStudyService.editResearchStudy(studyId, studyData);

        assertNull(result);

        verify(interactionService, times(1)).saveStudyInteraction(
                eq(studyId),
                eq(hubInteractionId),
                eq(StudyInteractionType.UPDATE),
                eq("Failed to Update a Study"),
                isNull(),
                isNull(),
                eq(studyData.data()),
                isNull(),
                argThat(errorJson -> errorJson.contains("Simulated exception")),
                eq(0),
                eq("Error"),
                eq(ActionType.UPDATE_STUDY),
                eq(FileProcessingStatus.FAILED));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveResearchStudySettings_validNctNumber() {
        ResearchStudySettingsRequest request = new ResearchStudySettingsRequest(
                "RS123",
                "Diabetes Study",
                "Description",
                "Location1",
                "Insulin Therapy",
                "NIH",
                "NCT12345678",
                LocalDate.now(),
                LocalDate.now().plusYears(1),
                "User123");

        JSONB mockResult = JSONB.valueOf("{\"status\": \"success\"}");

        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = researchStudyService.saveResearchStudySettings(request);

        assertEquals(mockResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
    }

    @Test
    void testSaveResearchStudySettings_invalidNctNumber() {
        ResearchStudySettingsRequest request = new ResearchStudySettingsRequest(
                "RS123",
                "Diabetes Study",
                "Description",
                "Location1",
                "Insulin Therapy",
                "NIH",
                "InvalidNCT123",
                LocalDate.now(),
                LocalDate.now().plusYears(1),
                "User123");

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            researchStudyService.saveResearchStudySettings(request);
        });

        String expectedMessage = "The provided NCT Number is invalid. It must start with 'NCT' followed by exactly 8 digits (e.g., NCT12345678).";
        assertEquals(expectedMessage, thrown.getMessage());
    }

    @Test
    void testSaveResearchStudySettings_emptyNctNumber() {
        ResearchStudySettingsRequest request = new ResearchStudySettingsRequest(
                "RS123",
                "Diabetes Study",
                "Description",
                "Location1",
                "Insulin Therapy",
                "NIH",
                "",
                LocalDate.now(),
                LocalDate.now().plusYears(1),
                "User123");

        ResearchStudyService researchStudyServiceMock = mock(ResearchStudyService.class);

        when(researchStudyServiceMock.saveResearchStudySettings(request)).thenThrow(
                new IllegalArgumentException(
                        "The provided NCT Number is invalid. It must start with 'NCT' followed by exactly 8 digits (e.g., NCT12345678)."));

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            researchStudyServiceMock.saveResearchStudySettings(request);
        });

        String expectedMessage = "The provided NCT Number is invalid. It must start with 'NCT' followed by exactly 8 digits (e.g., NCT12345678).";
        assertEquals(expectedMessage, thrown.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveResearchStudySettings_exceptionDuringSave() throws JsonMappingException, JsonProcessingException {
        ResearchStudySettingsRequest request = new ResearchStudySettingsRequest(
                "RS123",
                "Diabetes Study",
                "Description",
                "Location1",
                "Insulin Therapy",
                "NIH",
                "NCT12345678",
                LocalDate.now(),
                LocalDate.now().plusYears(1),
                "User123");

        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("DB error"));

        when(interactionService.getHubIntercationIdOfStudy(request.studyId())).thenReturn("HUB123");

        JSONB result = researchStudyService.saveResearchStudySettings(request);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode actualJson = mapper.readTree(result.data().toString());

        assertEquals("An error occurred while saving study settings. Please try again later.",
                actualJson.get("error").asText());
        assertEquals("error", actualJson.get("status").asText());

        verify(interactionService, times(1)).saveStudyInteraction(
                eq("RS123"),
                eq("HUB123"),
                eq(StudyInteractionType.UPDATE),
                eq(ActionDescription.UPDATE_STUDY_SETTINGS),
                eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.ACTIVE),
                eq(JsonUtils.toJson(request)),
                isNull(), // result.data() is null since query failed
                contains("DB error"),
                eq(200),
                eq("Success"),
                eq(ActionType.UPDATE_STUDY),
                eq(FileProcessingStatus.FAILED));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveResearchStudySettings_nullResultFromDb() {
        ResearchStudySettingsRequest request = new ResearchStudySettingsRequest(
                "RS123",
                "Diabetes Study",
                "Description",
                "Location1",
                "Insulin Therapy",
                "NIH",
                "NCT12345678",
                LocalDate.now(),
                LocalDate.now().plusYears(1),
                "User123");

        SelectSelectStep<Record1<String>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(JSONB.class)).thenReturn(null); // simulate DB returning null

        when(interactionService.getHubIntercationIdOfStudy(request.studyId())).thenReturn("HUB123");

        Object result = researchStudyService.saveResearchStudySettings(request);

        assertNull(result);

        verify(interactionService, times(1)).saveStudyInteraction(
                eq("RS123"),
                eq("HUB123"),
                eq(StudyInteractionType.UPDATE),
                eq(ActionDescription.UPDATE_STUDY_SETTINGS),
                eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.ACTIVE),
                eq(JsonUtils.toJson(request)),
                isNull(),
                eq("Failed to save or update research study settings"),
                eq(200),
                eq("Success"),
                eq(ActionType.UPDATE_STUDY),
                eq(FileProcessingStatus.FAILED));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudies_validResult() throws Exception {
        String studyId = "RS123";
        String jsonResponse = "{\"study_id\": \"RS123\", \"study_display_id\": \"DS123\", \"title\": \"Study Title\", \"description\": \"Study Description\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(fromMock);
        when(fromMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        JSONB result = researchStudyService.getResearchStudies(studyId).join();

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);

        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).selectDistinct(any(Field.class));
        verify(selectMock, times(1)).from(any(TableLike.class));
        verify(fromMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudies_noDataFound() {
        String studyId = "RS123";
        String expectedJson = "{}";
        JSONB expectedResult = JSONB.valueOf(expectedJson);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);
        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(fromMock);
        when(fromMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB actualResult = researchStudyService.getResearchStudies(studyId).join();

        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).selectDistinct(any(Field.class));
        verify(selectMock, times(1)).from(any(TableLike.class));
        verify(fromMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudies_jsonProcessingError() throws Exception {
        String studyId = "RS123";
        String invalidJson = "{\"study_id\": \"RS123\", \"study_display_id\": \"DS123\", \"title\": \"Study Title\"";
        JSONB mockResult = JSONB.valueOf(invalidJson);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(fromMock);
        when(fromMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        JSONB actualResult = researchStudyService.getResearchStudies(studyId).join();

        assertEquals(mockResult, actualResult);
        verify(dsl, times(1)).selectDistinct(any(Field.class));
        verify(selectMock, times(1)).from(any(TableLike.class));
        verify(fromMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetMyResearchStudies_successful() throws Exception {
        String userId = "user123";
        String jsonResponse = "{\"study_id\": \"RS123\", \"study_display_id\": \"DS123\", \"title\": \"Study Title\", \"description\": \"Study Description\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object result = researchStudyService.getMyResearchStudies(userId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
        assertEquals(expectedResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetMyResearchStudies_noDataFound() {
        String userId = "user123";
        String expectedJson = "{}";
        JSONB expectedResult = JSONB.valueOf(expectedJson);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);
        Object actualResult = researchStudyService.getMyResearchStudies(userId);

        assertEquals(expectedResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetMyResearchStudies_jsonProcessingError() throws Exception {
        String userId = "user123";
        String invalidJson = "{\"study_id\": \"RS123\", \"study_display_id\": \"DS123\", \"title\": \"Study Title\""; // Malformed
                                                                                                                      // JSON
        JSONB mockResult = JSONB.valueOf(invalidJson);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        ObjectMapper objectMapperMock = mock(ObjectMapper.class);
        when(objectMapperMock.readValue(anyString(), any(Class.class)))
                .thenThrow(new JsonProcessingException("JSON Parsing Error") {
                });
        Object actualResult = researchStudyService.getMyResearchStudies(userId);

        assertEquals(mockResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudies_successful() throws Exception {
        String userId = "user123";
        String jsonResponse = "[{\"description\":\"Study Description\",\"study_id\":\"RS123\",\"title\":\"Study Title\",\"study_display_id\":\"DS123\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = researchStudyService.getAllResearchStudies(userId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);

        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudies_noDataFound() {
        String userId = "user123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object actualResult = researchStudyService.getAllResearchStudies(userId);
        assertEquals(JSONB.valueOf("{}"), actualResult);
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudies_jsonProcessingError() throws Exception {
        String userId = "user123";
        String invalidJson = "{}"; // Malformed
                                   // JSON
        JSONB mockResult = JSONB.valueOf(invalidJson);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        ObjectMapper objectMapperMock = mock(ObjectMapper.class);
        when(objectMapperMock.readValue(anyString(), any(Class.class)))
                .thenThrow(new JsonProcessingException("JSON Parsing Error") {
                });
        Object actualResult = researchStudyService.getAllResearchStudies(userId);

        assertEquals(JSONB.valueOf("{}"), actualResult);
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testEditResearchStudyArchiveStatus_Success() {
        String studyId = "RS123";
        Boolean isArchived = true;
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "hub789";
        String activityData = "{\"activity\": \"archive study\"}";

        JSONB expectedResult = JSONB.valueOf("{\"status\": \"success\"}");

        HttpServletRequest mockRequest = mock(HttpServletRequest.class);
        when(auditService.getCurrentRequest()).thenReturn(mockRequest);

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        SelectSelectStep<Record1<Object>> selectDistinctMock = mock(SelectSelectStep.class);
        @NotNull
        SelectJoinStep<Record1<Object>> fromMock = mock(SelectJoinStep.class);
        @NotNull
        SelectConditionStep<Record1<Object>> whereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(DSL.field("created_by"))).thenReturn(selectDistinctMock);
        when(selectDistinctMock.from("drh_stateless_research_study.research_study_view")).thenReturn(fromMock);
        when(fromMock.where(DSL.field("study_id").eq(DSL.val(studyId)))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(userPartyId);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn(activityData);

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);

        SelectSelectStep<Record1<JSONB>> selectStepMock = mock(SelectSelectStep.class);

        Field<JSONB> functionField = DSL.field(
                "drh_stateless_research_study.update_research_study_archive_status({0}, {1}, {2}, {3})",
                JSONB.class,
                DSL.val(studyId),
                DSL.val(isArchived),
                DSL.val(userPartyId),
                DSL.cast(DSL.val(activityData), JSONB.class));

        when(dsl.select(functionField)).thenReturn(selectStepMock);
        when(selectStepMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB actualResult = researchStudyService.editResearchStudyArchiveStatus(studyId, isArchived);

        assertEquals(expectedResult, actualResult);
        verify(userNameService).getUserId();
        verify(interactionService).saveStudyInteraction(eq(studyId), eq(hubInteractionId), any(), any(), any(), any(),
                any(), eq(expectedResult.data()), any(), eq(200), eq("Success"), any(), any());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testEditResearchStudyArchiveStatus_whenException_thenSaveFailedInteractionAndReturnNull() {
        String studyId = "RS123";
        Boolean isArchived = true;
        String userId = "user123";
        String partyId = "party456";

        when(userNameService.getUserId()).thenReturn(userId);
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(partyId);
        // when(researchStudyService.getStudyCreatedBy(studyId)).thenReturn(partyId);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB failure"));
        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn("hub123");

        JSONB result = researchStudyService.editResearchStudyArchiveStatus(studyId, isArchived);

        assertNull(result);

        verify(interactionService).saveStudyInteraction(
                eq(studyId),
                eq("hub123"),
                eq(StudyInteractionType.UPDATE),
                eq(ActionDescription.UPDATE_ARCHIVE_STATUS),
                eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.ACTIVE),
                isNull(),
                isNull(),
                contains("return value of"),
                eq(0),
                eq("Error"),
                eq(ActionType.UPDATE_STUDY),
                eq(FileProcessingStatus.FAILED));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudyTeam_successful() throws Exception {
        String studyId = "RS123";
        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Lead Investigator\", \"study_id\": \"RS123\"}, {\"practitioner_party_id\": \"party456\", \"name\": \"Jane Doe\", \"role\": \"Co-Investigator\", \"study_id\": \"RS123\"}]}";

        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        Object actualResult = researchStudyService.getAllResearchStudyTeam(studyId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);

        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudyTeam_noDataFound() {
        String studyId = "RS123";

        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);
        Object actualResult = researchStudyService.getAllResearchStudyTeam(studyId);

        assertEquals(JSONB.valueOf("{}"), actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAllResearchStudyTeam_jsonProcessingError() throws Exception {
        String studyId = "RS123";
        String invalidJson = "\", \"role\": \"Lead Investigator\", \"study_id\": \"RS123\"}, {\"practitioner_party_id\": \"party456\", \"name\": \"Jane Doe\", \"role\": \"Co-Investigator\", \"study_id\": \"RS123\"}]";

        JSONB mockJsonbResult = JSONB.valueOf("{\"jsonb_agg\": " + invalidJson + "}");
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonbResult);

        ObjectMapper objectMapperMock = mock(ObjectMapper.class);
        when(objectMapperMock.readValue(anyString(), any(Class.class)))
                .thenThrow(new JsonProcessingException("JSON Parsing Error") {
                });
        Object actualResult = researchStudyService.getAllResearchStudyTeam(studyId);
        assertEquals(mockJsonbResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());

    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdatePublicationInline() {
        String studyId = "study-123";
        String citationId = "citation-123";
        PublicationUpdateRequest publicationUpdateRequest = new PublicationUpdateRequest(
                "Sample Title",
                java.sql.Date.valueOf(LocalDate.of(2025, 2, 3)),
                "10.1000/xyz123",
                "123445",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.DOI);
        String userId = "user123";
        String userPartyId = "party456";
        String jsonResponse = "{\"publication_title\":\"Sample Title\",\"publication_date\":\"2025-02-03\",\"publication_doi\":\"10.1000/xyz123\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        JSONB actualResult = researchStudyService.updatePublicationInline(studyId, publicationUpdateRequest,
                citationId);
        assertEquals(mockResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdatePublicationInlineExceptionHandling() {
        String studyId = "study-123";
        String citationId = "123445";
        PublicationUpdateRequest publicationUpdateRequest = new PublicationUpdateRequest(
                "Sample Title",
                java.sql.Date.valueOf(LocalDate.of(2025, 2, 3)),
                "10.1000/xyz123",
                "123445",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.DOI);

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            researchStudyService.updatePublicationInline(studyId, publicationUpdateRequest, citationId);
        });
        assertEquals("Error while processing the updatePublicationInline function", exception.getMessage());
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateVisibility() {
        String researchStudyId = "RS123";
        Integer studyVisibilityId = 1;
        String userId = "user123";
        String userPartyId = "party456";
        String jsonResponse = "{\"status\":\"success\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);
        String visibilityLabel = "Public";

        when(userNameService.getUserId()).thenReturn(userId);
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        when(masterService.getStudyVisibilityById(studyVisibilityId)).thenReturn(visibilityLabel);

        JSONB actualResult = researchStudyService.updateVisibility(researchStudyId, studyVisibilityId);

        assertEquals(mockResult, actualResult);

        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(userId);
        verify(dsl).select(any(Field.class));
        verify(selectMock).fetchOneInto(JSONB.class);
        verify(masterService).getStudyVisibilityById(studyVisibilityId);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateVisibility_whenExceptionThrown_thenSaveFailedInteractionAndThrow() {
        String researchStudyId = "RS123";
        Integer studyVisibilityId = 1;
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "hub123";
        String visibilityLabel = "Public";

        when(userNameService.getUserId()).thenReturn(userId);
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("DB failure"));
        when(interactionService.getHubIntercationIdOfStudy(researchStudyId)).thenReturn(hubInteractionId);
        when(masterService.getStudyVisibilityById(studyVisibilityId)).thenReturn(visibilityLabel);

        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> researchStudyService.updateVisibility(researchStudyId, studyVisibilityId));

        assertEquals("Error while processing the updateVisibility function", exception.getMessage());

        verify(interactionService).saveStudyInteraction(
                eq(researchStudyId),
                eq(hubInteractionId),
                eq(StudyInteractionType.UPDATE),
                eq("Update study visibility to Public."),
                eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.ACTIVE),
                isNull(),
                isNull(),
                contains("DB failure"),
                eq(0),
                eq("Error"),
                eq(ActionType.UPDATE_STUDY),
                eq(FileProcessingStatus.FAILED));

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyfArchieveStatus() {
        String studyId = "RS123";
        Boolean expectedStatus = true;

        SelectSelectStep<Record1<Boolean>> localSelectMock = (SelectSelectStep<Record1<Boolean>>) mock(
                SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> localJoinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> localWhereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(localSelectMock);
        when(localSelectMock.from(anyString())).thenReturn(localJoinMock);
        when(localJoinMock.where(any(org.jooq.Condition.class))).thenReturn(localWhereMock);
        when(localWhereMock.fetchOneInto(Boolean.class)).thenReturn(expectedStatus);

        Object actualStatus = researchStudyService.getResearchStudyArchiveStatus(studyId);

        assertEquals(expectedStatus, actualStatus);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyArchieveStatus() {
        String studyId = "RS123";
        Boolean expectedStatus = true;

        SelectSelectStep<Record1<JSONB>> selectStepMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<?> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(SelectField.class))).thenReturn(selectStepMock);
        when(selectStepMock.from(any(String.class))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class)))
                .thenReturn((SelectConditionStep<Record1<JSONB>>) conditionStepMock);
        when(conditionStepMock.fetchOneInto(Boolean.class)).thenReturn(expectedStatus);

        Object actualStatus = researchStudyService.getResearchStudyArchiveStatus(studyId);

        assertEquals(expectedStatus, actualStatus);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyDisplayId() {
        String studyId = "RS123";
        String expectedDisplayId = "SD123";

        SelectSelectStep<Record1<String>> localSelectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> localJoinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> localConditionMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(localSelectMock);
        when(localSelectMock.from(anyString())).thenReturn(localJoinMock);
        when(localJoinMock.where(any(org.jooq.Condition.class))).thenReturn(localConditionMock);
        when(localConditionMock.fetchOneInto(String.class)).thenReturn(expectedDisplayId);

        String actualDisplayId = researchStudyService.getStudyDisplayId(studyId);

        assertEquals(expectedDisplayId, actualDisplayId);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigatorOrAuthor_successful() throws Exception {
        String studyId = "RS123";
        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Author\", \"study_id\": \"RS123\"}, {\"practitioner_party_id\": \"party456\", \"name\": \"Jane Doe\", \"role\": \"Co-Author\", \"study_id\": \"RS123\"}]}";
        String roleIndex = "Author";

        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        Object result = researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigatorOrAuthor_noDataFound() throws InterruptedException, ExecutionException {
        String studyId = "RS123";
        String roleIndex = "Author";
        String expectedJson = "{}";
        JSONB expectedResult = JSONB.valueOf(expectedJson);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);
        Object actualResult = researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex);

        assertEquals(expectedResult, actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigatorOrAuthor_jsonProcessingError() throws Exception {
        String studyId = "RS123";
        String invalidJson = "\", \"role\": \"Lead Investigator\", \"study_id\": \"RS123\"}, {\"practitioner_party_id\": \"party456\", \"name\": \"Jane Doe\", \"role\": \"Co-Investigator\", \"study_id\": \"RS123\"}]";
        String roleIndex = "Author";

        JSONB mockJsonbResult = JSONB.valueOf("{\"jsonb_agg\": " + invalidJson + "}");
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonbResult);

        ObjectMapper objectMapperMock = mock(ObjectMapper.class);
        when(objectMapperMock.readValue(anyString(), any(Class.class)))
                .thenThrow(new JsonProcessingException("JSON Parsing Error") {
                });
        Object actualResult = researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex);
        assertEquals(mockJsonbResult, actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());

    }

    @Test
    void testGetAllResearchStudyCoInvestigator() throws Exception {
        String studyId = "RS123";
        String roleIndex = "Co-Investigator";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Co-Investigator\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);
        when(researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex)).thenReturn(mockJsonbResult);
        Object result = researchStudyService.getAllResearchStudyCoInvestigator(studyId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    void testGetAllResearchStudyPrincipalAuthor() throws Exception {
        String studyId = "RS123";
        String roleIndex = "Principal Author";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Principal Author\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);
        when(researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex)).thenReturn(mockJsonbResult);
        Object result = researchStudyService.getAllResearchStudyPrincipalAuthor(studyId);
        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    void testGetAllResearchStudyCoAuthor() throws Exception {
        String studyId = "RS123";
        String roleIndex = "Co-Author";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Co-Author\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);

        doReturn(mockJsonbResult).when(researchStudyService).getInvestigatorOrAuthor(studyId, roleIndex);
        Object result = researchStudyService.getAllResearchStudyCoAuthor(studyId);

        JSONB actualResult = (JSONB) result;
        assertEquals(mockJsonbResult, actualResult);
    }

    @Test
    void testGetAllResearchStudyPrincipalInvestigator() throws Exception {
        String studyId = "RS123";
        String principalInvestigatorRoleIndex = "Primary-investigator";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Primary-investigator\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);
        when(researchStudyService.getInvestigatorOrAuthor(studyId, principalInvestigatorRoleIndex))
                .thenReturn(mockJsonbResult);
        Object result = researchStudyService.getAllResearchStudyPrincipalInvestigator(studyId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetParticipantDisplayId() {
        String participantId = "RS123";
        String expectedDisplayId = "SD123";

        SelectSelectStep<Record1<String>> localSelectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> localJoinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> localConditionMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(localSelectMock);
        when(localSelectMock.from(anyString())).thenReturn(localJoinMock);
        when(localJoinMock.where(any(org.jooq.Condition.class))).thenReturn(localConditionMock);
        when(localConditionMock.fetchOneInto(String.class)).thenReturn(expectedDisplayId);

        String actualDisplayId = researchStudyService.getParticipantDisplayId(participantId);
        assertEquals(expectedDisplayId, actualDisplayId);
    }

    @Test
    void testGetStudyTeamMembers() throws Exception {
        String studyId = "RS123";
        String roleIndex = "Study Team Member";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Study Team Member\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);
        when(researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex)).thenReturn(mockJsonbResult);
        Object result = researchStudyService.getStudyTeamMembers(studyId);
        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    void testGetNominatedPrincipalInvestigator() throws Exception {
        String studyId = "RS123";
        String roleIndex = "Nominated-Principal Investigator";

        String jsonResponse = "{\"jsonb_agg\": [{\"practitioner_party_id\": \"party123\", \"name\": \"John Doe\", \"role\": \"Nominated-Principal Investigator\", \"study_id\": \"RS123\"}]}";
        JSONB mockJsonbResult = JSONB.valueOf(jsonResponse);
        when(researchStudyService.getInvestigatorOrAuthor(studyId, roleIndex)).thenReturn(mockJsonbResult);
        Object result = researchStudyService.getNominatedPrincipalInvestigator(studyId);

        JSONB expectedResult = JSONB.valueOf(jsonResponse);
        JSONB actualResult = (JSONB) result;
        assertEquals(expectedResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyOwner_found() {
        String studyId = "RS123";
        String expectedOwner = "<EMAIL>";

        SelectSelectStep<Record1<String>> selectMock = Mockito.mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = Mockito.mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = Mockito.mock(SelectConditionStep.class);

        when(dsl.selectDistinct(Mockito.any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(Mockito.anyString())).thenReturn(fromMock);
        when(fromMock.where(Mockito.any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(expectedOwner);

        String actualOwner = researchStudyService.getStudyOwner(studyId);

        assertEquals(expectedOwner, actualOwner);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyOwner_notFound() {
        String studyId = "RS999";
        SelectSelectStep<Record1<String>> selectDistinctMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(Mockito.any(Field.class))).thenReturn(selectDistinctMock);
        when(selectDistinctMock.from(Mockito.anyString())).thenReturn(fromMock);
        when(fromMock.where(Mockito.any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(null);

        String actualOwner = researchStudyService.getStudyOwner(studyId);

        assertNull(actualOwner);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckStudyDisplayIdExists_whenExists() {
        String studyDisplayId = "DISP123";

        var selectMock = Mockito.mock(org.jooq.SelectSelectStep.class);
        var fromMock = Mockito.mock(org.jooq.SelectJoinStep.class);
        var whereMock = Mockito.mock(org.jooq.SelectConditionStep.class);

        when(dsl.selectCount()).thenReturn(selectMock);
        when(selectMock.from(Mockito.anyString())).thenReturn(fromMock);
        when(fromMock.where(Mockito.any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, Integer.class)).thenReturn(1); // simulate count > 0

        boolean exists = researchStudyService.checkStudyDisplayIdExists(studyDisplayId);

        assertEquals(true, exists);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckStudyDisplayIdExists_whenNotExists() {
        String studyDisplayId = "NON_EXISTENT";

        var selectMock = Mockito.mock(org.jooq.SelectSelectStep.class);
        var fromMock = Mockito.mock(org.jooq.SelectJoinStep.class);
        var whereMock = Mockito.mock(org.jooq.SelectConditionStep.class);

        when(dsl.selectCount()).thenReturn(selectMock);
        when(selectMock.from(Mockito.anyString())).thenReturn(fromMock);
        when(fromMock.where(Mockito.any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, Integer.class)).thenReturn(0); // simulate count = 0

        boolean exists = researchStudyService.checkStudyDisplayIdExists(studyDisplayId);

        assertEquals(false, exists);
    }

    // Test cases for deleteResearchStudy method
    @SuppressWarnings("unchecked")
    @Test
    void testDeleteResearchStudy_success() {
        String studyId = "RS123";
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "hub789";
        String studyCreatedBy = "party456"; // Same as userPartyId
        JSONB expectedResult = JSONB.valueOf("{\"status\": \"success\"}");

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);
        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        // Mock getStudyCreatedBy method
        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(studyCreatedBy);

        // Mock delete query
        SelectSelectStep<Record1<String>> deleteQueryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(deleteQueryMock);
        when(deleteQueryMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = researchStudyService.deleteResearchStudy(studyId);

        assertEquals(expectedResult, result);
        verify(interactionService).saveStudyInteraction(
                eq(studyId), eq(hubInteractionId), eq(StudyInteractionType.DELETE),
                eq(ActionDescription.DELETE_STUDY), eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.DELETED), isNull(), eq(expectedResult.data()),
                isNull(), eq(200), eq("Success"), eq(ActionType.DELETE_STUDY),
                eq(FileProcessingStatus.SUCCESS));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testDeleteResearchStudy_permissionDenied() {
        String studyId = "RS123";
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "hub789";
        String studyCreatedBy = "differentParty"; // Different from userPartyId

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);
        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        // Mock getStudyCreatedBy method
        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(studyCreatedBy);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            researchStudyService.deleteResearchStudy(studyId);
        });

        assertEquals("You don't have permission to delete this Study.", exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testDeleteResearchStudy_exceptionDuringDelete() {
        String studyId = "RS123";
        String userId = "user123";
        String userPartyId = "party456";
        String hubInteractionId = "hub789";
        String studyCreatedBy = "party456";

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);
        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        // Mock getStudyCreatedBy method
        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(String.class)).thenReturn(studyCreatedBy);

        // Mock delete query to throw exception
        SelectSelectStep<Record1<String>> deleteQueryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(deleteQueryMock);
        when(deleteQueryMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("DB error"));

        JSONB result = researchStudyService.deleteResearchStudy(studyId);

        assertNull(result);
        verify(interactionService).saveStudyInteraction(
                eq(studyId), eq(hubInteractionId), eq(StudyInteractionType.DELETE),
                eq(ActionDescription.DELETE_STUDY), eq(ResearchStudyState.ACTIVE),
                eq(ResearchStudyState.DELETED), isNull(), isNull(),
                contains("DB error"), eq(0), eq("Error"), eq(ActionType.DELETE_STUDY),
                eq(FileProcessingStatus.FAILED));
    }

    // Test cases for getStudyCitations method
    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyCitations_success() throws Exception {
        String studyId = "RS123";
        String jsonResponse = "[{\"study_id\":\"RS123\",\"citation_id\":\"C1\",\"publication_title\":\"Test Publication\",\"publication_date\":\"01-15-2024\",\"publication_doi\":\"10.1000/test\",\"pubmed_id\":\"12345\",\"citation_authors\":[\"Author 1\",\"Author 2\"],\"created_at\":\"2024-01-15T10:00:00\",\"citation_data_source\":\"DOI\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, JSONB.class)).thenReturn(mockResult);

        JSONB result = researchStudyService.getStudyCitations(studyId).get();

        assertEquals(mockResult, result);
        verify(dsl).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyCitations_noDataFound() throws Exception {
        String studyId = "RS123";

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, JSONB.class)).thenReturn(null);

        JSONB result = researchStudyService.getStudyCitations(studyId).get();

        assertEquals(JSONB.jsonb("[]"), result);
        verify(dsl).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyCitations_emptyData() throws Exception {
        String studyId = "RS123";
        JSONB emptyResult = JSONB.valueOf("");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, JSONB.class)).thenReturn(emptyResult);

        JSONB result = researchStudyService.getStudyCitations(studyId).get();

        assertEquals(JSONB.jsonb("[]"), result);
        verify(dsl).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyCitations_exceptionHandling() throws Exception {
        String studyId = "RS123";

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOne(0, JSONB.class)).thenThrow(new RuntimeException("DB error"));

        JSONB result = researchStudyService.getStudyCitations(studyId).get();

        assertEquals(JSONB.jsonb("[]"), result);
        verify(dsl).select(any(Field.class));
    }

    // Test cases for saveStudyCitation method
    @SuppressWarnings("unchecked")
    @Test
    void testSaveStudyCitation_success() throws Exception {
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Test Publication",
                java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                "10.1000/test",
                "12345",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.DOI);
        String userId = "user123";
        String userPartyId = "party456";
        String activityData = "{\"activity\": \"save citation\"}";

        String jsonResponse = "{\"status\":\"success\",\"citation_id\":\"C123\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn(activityData);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Map<String, Object> result = researchStudyService.saveStudyCitation(request, studyId);

        assertEquals("success", result.get("status"));
        assertEquals("C123", result.get("citation_id"));
        verify(dsl).select(any(Field.class));
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(userId);
        verify(activityLogService).prepareActivityLogMetadata();
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveStudyCitation_nullResult() {
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Test Publication",
                java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                "10.1000/test",
                "12345",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.DOI);
        String userId = "user123";
        String userPartyId = "party456";
        String activityData = "{\"activity\": \"save citation\"}";

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn(activityData);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Map<String, Object> result = researchStudyService.saveStudyCitation(request, studyId);

        assertEquals("error", result.get("status"));
        assertEquals("No data returned", result.get("message"));
        verify(dsl).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveStudyCitation_exceptionHandling() {
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Test Publication",
                java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                "10.1000/test",
                "12345",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.DOI);

        when(userNameService.getUserId()).thenThrow(new RuntimeException("User service error"));

        Map<String, Object> result = researchStudyService.saveStudyCitation(request, studyId);

        assertEquals("error", result.get("status"));
        assertEquals("Failed to save study citation", result.get("message"));
        verify(userNameService).getUserId();
    }

    // Test cases for saveAuthors method
    @SuppressWarnings("unchecked")
    @Test
    void testSaveAuthors_success() {
        String studyId = "RS123";
        String[] authorNames = { "Author 1", "Author 2", "Author 3" };
        String citationId = "C123";
        String activityData = "{\"activity\": \"save authors\"}";
        String jsonResponse = "{\"status\":\"success\",\"authors_saved\":3}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(activityLogService.prepareActivityLogMetadata()).thenReturn(activityData);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object result = researchStudyService.saveAuthors(studyId, authorNames, citationId);

        assertEquals(jsonResponse, result);
        verify(dsl).select(any(Field.class));
        verify(activityLogService).prepareActivityLogMetadata();
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAuthors_nullResult() {
        String studyId = "RS123";
        String[] authorNames = { "Author 1", "Author 2" };
        String citationId = "C123";
        String activityData = "{\"activity\": \"save authors\"}";

        when(activityLogService.prepareActivityLogMetadata()).thenReturn(activityData);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object result = researchStudyService.saveAuthors(studyId, authorNames, citationId);

        assertEquals("{}", result);
        verify(dsl).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAuthors_exceptionHandling() {
        String studyId = "RS123";
        String[] authorNames = { "Author 1" };
        String citationId = "C123";

        when(activityLogService.prepareActivityLogMetadata()).thenThrow(new RuntimeException("Activity service error"));

        Object result = researchStudyService.saveAuthors(studyId, authorNames, citationId);

        JSONB expectedError = JSONB.jsonb("{\"status\": \"error\", \"message\": \"Failed to save citation authors\"}");
        assertEquals(expectedError, result);
        verify(activityLogService).prepareActivityLogMetadata();
    }

    // Test cases for isDuplicatePubMedOrDOI method
    @Test
    void testIsDuplicatePubMedOrDOI_withBothDoiAndPubmedId_duplicateFound() {
        String studyId = "RS123";
        String publicationDoi = "10.1000/test";
        String pubmedId = "12345";
        String citationId = "C123";

        // Use spy to mock the actual method behavior
        ResearchStudyService spyService = Mockito.spy(researchStudyService);
        Mockito.doReturn(true).when(spyService).isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        Boolean result = spyService.isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        assertEquals(true, result);
    }

    @Test
    void testIsDuplicatePubMedOrDOI_withOnlyPubmedId_noDuplicate() {
        String studyId = "RS123";
        String publicationDoi = null;
        String pubmedId = "12345";
        String citationId = null;

        // Use spy to mock the actual method behavior
        ResearchStudyService spyService = Mockito.spy(researchStudyService);
        Mockito.doReturn(false).when(spyService).isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        Boolean result = spyService.isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        assertEquals(false, result);
    }

    @Test
    void testIsDuplicatePubMedOrDOI_withOnlyDoi_duplicateFound() {
        String studyId = "RS123";
        String publicationDoi = "10.1000/test";
        String pubmedId = null;
        String citationId = "C123";

        // Use spy to mock the actual method behavior
        ResearchStudyService spyService = Mockito.spy(researchStudyService);
        Mockito.doReturn(true).when(spyService).isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        Boolean result = spyService.isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        assertEquals(true, result);
    }

    @Test
    void testIsDuplicatePubMedOrDOI_withNeitherDoiNorPubmedId_returnsFalse() {
        String studyId = "RS123";
        String publicationDoi = null;
        String pubmedId = null;
        String citationId = "C123";

        Boolean result = researchStudyService.isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        assertEquals(false, result);
        // No database interaction should occur
        verify(dsl, times(0)).selectOne();
    }

    @Test
    void testIsDuplicatePubMedOrDOI_withEmptyStrings_returnsFalse() {
        String studyId = "RS123";
        String publicationDoi = "";
        String pubmedId = "";
        String citationId = "C123";

        Boolean result = researchStudyService.isDuplicatePubMedOrDOI(studyId, publicationDoi, pubmedId, citationId);

        assertEquals(false, result);
        // No database interaction should occur
        verify(dsl, times(0)).selectOne();
    }

    // Test cases for updateStudyCitation method
    @SuppressWarnings("unchecked")
    @Test
    void testUpdateStudyCitation_success() throws Exception {
        String citationId = "C123";
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Updated Publication Title",
                java.sql.Date.valueOf(LocalDate.of(2024, 2, 15)),
                "10.1000/updated",
                "54321",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.PubMed);
        String userId = "user123";
        String userPartyId = "party456";

        String jsonResponse = "{\"status\":\"success\",\"citation_id\":\"C123\",\"updated\":true}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Map<String, Object> result = researchStudyService.updateStudyCitation(citationId, request, studyId);

        assertEquals("success", result.get("status"));
        assertEquals("C123", result.get("citation_id"));
        assertEquals(true, result.get("updated"));
        verify(dsl).select(any(Field.class));
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(userId);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateStudyCitation_nullResult() {
        String citationId = "C123";
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Updated Publication Title",
                java.sql.Date.valueOf(LocalDate.of(2024, 2, 15)),
                "10.1000/updated",
                "54321",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.PubMed);
        String userId = "user123";
        String userPartyId = "party456";

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(userPartyId);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Map<String, Object> result = researchStudyService.updateStudyCitation(citationId, request, studyId);

        assertEquals("error", result.get("status"));
        assertEquals("No data returned", result.get("message"));
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testUpdateStudyCitation_exceptionHandling() {
        String citationId = "C123";
        String studyId = "RS123";
        PublicationUpdateRequest request = new PublicationUpdateRequest(
                "Updated Publication Title",
                java.sql.Date.valueOf(LocalDate.of(2024, 2, 15)),
                "10.1000/updated",
                "54321",
                org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource.PubMed);

        when(userNameService.getUserId()).thenThrow(new RuntimeException("User service error"));

        Map<String, Object> result = researchStudyService.updateStudyCitation(citationId, request, studyId);

        assertEquals("error", result.get("status"));
        assertEquals("Failed to save study citation", result.get("message"));
        verify(userNameService).getUserId();
    }

}
