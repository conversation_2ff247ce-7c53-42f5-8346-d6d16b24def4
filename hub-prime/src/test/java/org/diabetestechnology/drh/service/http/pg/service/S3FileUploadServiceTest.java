package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.json.JSONObject;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.jooq.JSONB;
import org.json.JSONArray;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Path;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.nio.file.Paths;

import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Utilities;
import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

@ExtendWith(MockitoExtension.class)
public class S3FileUploadServiceTest {

    @InjectMocks
    private S3FileUploadService s3FileUploadService;

    @Mock
    private S3Client s3Client;

    @Mock
    private MasterService masterService;

    @Mock
    private S3Utilities s3Utilities;

    @TempDir
    Path tempDir;

    private String tempFileLocation;
    private ObjectMapper objectMapper;
    private static final String BUCKET_NAME = "test-bucket";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        tempFileLocation = tempDir.toString();
        objectMapper = new ObjectMapper();
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", BUCKET_NAME);

    }

    @SuppressWarnings("deprecation")
    @Test
    void testUploadFile_Success() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.csv", "text/csv", "id,name\n1,John".getBytes());

        URL mockUrl = new URL("https://mock-s3-url.com/participants/test.csv");
        when(s3Client.utilities()).thenReturn(s3Utilities);
        when(s3Client.utilities().getUrl(any(GetUrlRequest.class))).thenReturn(mockUrl);
        Field bucketField = S3FileUploadService.class.getDeclaredField("bucketName");
        bucketField.setAccessible(true);
        bucketField.set(s3FileUploadService, "test-bucket");
        Response response = s3FileUploadService.uploadFile(file);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("File Uploaded", response.getMessage());
        assertTrue(response.getData().containsKey("fileURL"));
    }

    @Test
    void testUploadFile_EmptyFile() throws IOException {
        MockMultipartFile emptyFile = new MockMultipartFile("file", "empty.csv", "text/csv",
                new byte[0]);
        Response response = s3FileUploadService.uploadFile(emptyFile);
        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("File is empty", response.getMessage());
    }

    @Test
    void testUploadFile_InvalidFileType() throws IOException {
        MockMultipartFile invalidFile = new MockMultipartFile("file", "test.txt",
                "text/csv",
                "test content".getBytes());

        Response response = s3FileUploadService.uploadFile(invalidFile);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("Invalid file type. Only CSV files are allowed.", response.getMessage());
    }

    @Test
    void testUploadFile_InvalidContentType() throws IOException {
        MockMultipartFile invalidFile = new MockMultipartFile("file", "test.csv",
                "text/wrongContentType",
                "test content".getBytes());

        Response response = s3FileUploadService.uploadFile(invalidFile);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("Invalid content type. Only CSV files are allowed.", response.getMessage());
    }

    @Test
    void testUploadFile_S3Exception() throws IOException {
        MockMultipartFile file = new MockMultipartFile("file", "test.csv", "text/csv", "id,name\n1,John".getBytes());

        S3Exception s3Exception = (S3Exception) S3Exception.builder().message("S3 error").build();

        doThrow(s3Exception).when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
        Response response = s3FileUploadService.uploadFile(file);

        assertNotNull(response);
        assertEquals("error", response.getStatus());
        assertEquals("Error uploading file to S3.", response.getMessage());
    }

    @Test
    void testUploadFile_GeneralException() throws IOException {
        MockMultipartFile file = new MockMultipartFile("file", "test.csv",
                "text/csv", "id,name\n1,John".getBytes());

        doThrow(new RuntimeException("Unexpected error")).when(s3Client).putObject(any(PutObjectRequest.class),
                any(RequestBody.class));

        Response response = s3FileUploadService.uploadFile(file);

        assertNotNull(response);
        assertEquals("error", response.getStatus());
        assertEquals("Unexpected error uploading file to S3.",
                response.getMessage());
    }

    @Test
    void testConvertToByteArray_Success() throws IOException {
        Path tempFile = Files.createTempFile(tempDir, "test-file", ".txt");
        Files.writeString(tempFile, "Sample content");

        byte[] content = s3FileUploadService.convertToByteArray(tempFile.toString());
        assertNotNull(content);
        assertEquals("Sample content", new String(content));
    }

    @Test
    void testConvertToByteArray_FileNotFound() {
        String nonExistentFilePath = tempDir.resolve("nonexistent.txt").toString();
        IOException thrown = assertThrows(IOException.class,
                () -> s3FileUploadService.convertToByteArray(nonExistentFilePath));
        assertEquals("File does not exist", thrown.getMessage());
    }

    @Test
    void testSaveFileToTempLocation_Success() throws Exception {

        MockMultipartFile mockFile = new MockMultipartFile("file", "test.csv", "text/csv", "test content".getBytes());

        Field tempFileLocationField = S3FileUploadService.class.getDeclaredField("tempFileLocation");
        tempFileLocationField.setAccessible(true);
        tempFileLocationField.set(s3FileUploadService, System.getProperty("java.io.tmpdir"));

        String savedFilePath = s3FileUploadService.saveFileToTempLocation(mockFile);
        File savedFile = new File(savedFilePath);

        assertTrue(savedFile.exists());
        assertEquals("test content", Files.readString(savedFile.toPath()));
    }

    @Test
    void testDeleteTempFile_Success() throws IOException {
        Path tempFile = Files.createTempFile(tempDir, "temp-file", ".csv");
        assertTrue(Files.exists(tempFile));

        s3FileUploadService.deleteTempFile(tempFile.toString());
        assertFalse(Files.exists(tempFile));
    }

    @Test
    void testDeleteTempFile_FileNotExists() {
        String nonExistentFilePath = tempDir.resolve("nonexistent.txt").toString();
        assertDoesNotThrow(() -> s3FileUploadService.deleteTempFile(nonExistentFilePath));
    }

    @Test
    void testValidateParticipantFileJson_AllValid() {

        when(masterService.readGenderType()).thenReturn(
                JSONB.valueOf("[{\"value\": \"Male\"}, {\"value\": \"Female\"}, {\"value\": \"Other\"}]"));
        ArrayNode jsonArray = objectMapper.createArrayNode();

        ObjectNode validRecord = objectMapper.createObjectNode();
        validRecord.put("participant_id", "12345");
        validRecord.put("age", "25");
        validRecord.put("gender", "Male");

        jsonArray.add(validRecord);

        String result = s3FileUploadService.validateParticipantFileJson(jsonArray);
        assertEquals("All records are valid.", result);
    }

    @Test
    void testValidateParticipantFileJson_MissingParticipantId() {
        ArrayNode jsonArray = objectMapper.createArrayNode();

        ObjectNode invalidRecord = objectMapper.createObjectNode();
        invalidRecord.put("age", "30");
        invalidRecord.put("gender", "Female");

        jsonArray.add(invalidRecord);

        String result = s3FileUploadService.validateParticipantFileJson(jsonArray);
        assertEquals("participant_id is missing or empty for row 1 .", result);
    }

    @Test
    void testValidateParticipantFileJson_Missing_Age() {
        ArrayNode jsonArray = objectMapper.createArrayNode();

        ObjectNode invalidRecord = objectMapper.createObjectNode();
        invalidRecord.put("participant_id", "12345");
        invalidRecord.put("gender", "Male");

        jsonArray.add(invalidRecord);

        String result = s3FileUploadService.validateParticipantFileJson(jsonArray);
        assertEquals("age is missing or empty for row 1 .", result);
    }

    @Test
    void testValidateParticipantFileJson_InvalidAge() {
        ArrayNode jsonArray = objectMapper.createArrayNode();

        ObjectNode invalidRecord = objectMapper.createObjectNode();
        invalidRecord.put("participant_id", "789");
        invalidRecord.put("age", "not_a_number");
        invalidRecord.put("gender", "Male");

        jsonArray.add(invalidRecord);

        String result = s3FileUploadService.validateParticipantFileJson(jsonArray);
        assertEquals("Age is not a valid number for row 1.", result);
    }

    @Test
    void testValidateParticipantFileJson_MissingGender() {
        ArrayNode jsonArray = objectMapper.createArrayNode();

        ObjectNode invalidRecord = objectMapper.createObjectNode();
        invalidRecord.put("participant_id", "456");
        invalidRecord.put("age", "40");

        jsonArray.add(invalidRecord);

        String result = s3FileUploadService.validateParticipantFileJson(jsonArray);
        assertEquals("gender is missing or empty for row 1 .", result);
    }

    @SuppressWarnings("deprecation")
    @Test
    void testUploadCgmFile_Success() throws IOException {

        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", "test content".getBytes());

        String orgId = "org123";
        String studyId = "study456";
        String s3FileName = String.format("%s/%s/cgm-meta-data/unique-test.csv", orgId, studyId);
        String expectedUrl = String.format("https://s3.amazonaws.com/test-bucket/%s", s3FileName);

        S3Utilities mockUtilities = mock(S3Utilities.class);
        when(s3Client.utilities()).thenReturn(mockUtilities);
        when(mockUtilities.getUrl(any(GetUrlRequest.class))).thenReturn(new URL(expectedUrl));

        String result = s3FileUploadService.uploadCgmFile(mockFile, orgId, studyId);

        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        assertNotNull(result, "File upload result should not be null");
        assertTrue(result.contains(s3FileName), "Returned URL should contain the expected file path");
    }

    @Test
    void testUploadCgmFile_SandboxMode() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "isSandbox", true);

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", "test content".getBytes());

        String result = s3FileUploadService.uploadCgmFile(mockFile, "org123", "study456");
        assertEquals("dummyUrl", result, "Expected dummyUrl in sandbox mode");
    }

    @Test
    void testUploadCgmFile_S3Exception() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", "test content".getBytes());

        doThrow(S3Exception.builder().message("S3 upload failed").build())
                .when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        String result = s3FileUploadService.uploadCgmFile(mockFile, "org123", "study456");

        assertNull(result, "Expected null when S3 upload fails");
    }

    @Test
    void testUploadCgmFile_GenericException() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", "test content".getBytes());

        doThrow(new RuntimeException("Unexpected error"))
                .when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        String result = s3FileUploadService.uploadCgmFile(mockFile, "org123", "study456");
        assertNull(result, "Expected null when an unexpected error occurs");
    }

    @SuppressWarnings("deprecation")
    @Test
    void testUploadParticipantFileToS3Bucket_Success() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "participants.csv", "text/csv", "participant data".getBytes());

        String orgId = "org123";
        String studyId = "study456";
        String s3FileName = String.format("%s/%s/participants/unique-participants.csv", orgId, studyId);
        String expectedUrl = String.format("https://s3.amazonaws.com/test-bucket/%s", s3FileName);

        S3Utilities mockUtilities = mock(S3Utilities.class);
        when(s3Client.utilities()).thenReturn(mockUtilities);
        when(mockUtilities.getUrl(any(GetUrlRequest.class))).thenReturn(new URL(expectedUrl));

        String result = s3FileUploadService.uploadParticipantFileToS3Bucket(mockFile, orgId, studyId);

        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
        assertNotNull(result, "File upload result should not be null");
        assertTrue(result.contains(s3FileName), "Returned URL should contain the expected file path");
    }

    @Test
    void testUploadParticipantFileToS3Bucket_SandboxMode() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "isSandbox", true);

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "participants.csv", "text/csv", "participant data".getBytes());

        String result = s3FileUploadService.uploadParticipantFileToS3Bucket(mockFile, "org123", "study456");
        assertEquals("dummyUrl", result, "Expected dummyUrl in sandbox mode");
    }

    @Test
    void testUploadParticipantFileToS3Bucket_S3Exception() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "participants.csv", "text/csv", "participant data".getBytes());

        doThrow(S3Exception.builder().message("S3 upload failed").build())
                .when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        String result = s3FileUploadService.uploadParticipantFileToS3Bucket(mockFile, "org123", "study456");

        assertNull(result, "Expected null when S3 upload fails");
    }

    @Test
    void testUploadParticipantFileToS3Bucket_GenericException() throws IOException {
        ReflectionTestUtils.setField(s3FileUploadService, "bucketName", "test-bucket");

        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "participants.csv", "text/csv", "participant data".getBytes());

        doThrow(new RuntimeException("Unexpected error"))
                .when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        String result = s3FileUploadService.uploadParticipantFileToS3Bucket(mockFile, "org123", "study456");

        assertNull(result, "Expected null when an unexpected error occurs");
    }

    @Test
    void testGenerateCsvTemplate_Success() {

        String expectedCsvContent = "participant_id,diagnosis_icd,med_rxnorm,treatment_modality,gender,race,ethnicity,age,bmi,baseline_hba1c,diabetes_type,study_arm\n";
        byte[] expectedBytes = expectedCsvContent.getBytes();

        byte[] result = s3FileUploadService.generateCsvTemplate();
        assertNotNull(result, "CSV template byte array should not be null");
        assertArrayEquals(expectedBytes, result, "Generated CSV template should match expected content");
    }

    @Test
    void testGenerateCsvTemplate_ExceptionHandling() {
        S3FileUploadService service = new S3FileUploadService() {
            @Override
            public byte[] generateCsvTemplate() {
                throw new RuntimeException("Failed to generate CSV template");
            }
        };

        RuntimeException exception = assertThrows(RuntimeException.class, service::generateCsvTemplate);

        assertEquals("Failed to generate CSV template", exception.getMessage());
    }

    @Test
    void testProcessCsvFile_Success() throws Exception {
        Path csvFile = tempDir.resolve("test.csv");
        Files.writeString(csvFile, "id,name,age\n1,John,30\n2,Jane,25");

        JSONArray result = s3FileUploadService.processContent(csvFile.toString());

        assertNotNull(result);
        assertTrue(result.length() > 0, "CSV should be converted to JSON.");
    }

    @Test
    void testProcessContent_UnsupportedFileType() {
        Exception exception = assertThrows(RuntimeException.class, () -> {
            s3FileUploadService.processContent("unsupported.xyz");
        });

        assertTrue(exception.getMessage().contains("Error processing file content"),
                "Should throw error for unsupported file.");
    }

    @Test
    void testProcessExcelFile_Success() throws Exception {

        Path excelFile = tempDir.resolve("test.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Sheet1");

            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("id");
            headerRow.createCell(1).setCellValue("name");
            headerRow.createCell(2).setCellValue("age");

            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue(1);
            row1.createCell(1).setCellValue("John");
            row1.createCell(2).setCellValue(30);

            try (OutputStream os = Files.newOutputStream(excelFile)) {
                workbook.write(os);
            }
        }

        JSONArray result = s3FileUploadService.processContent(excelFile.toString());

        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.length(), "There should be two entries in the JSON array");

        JSONObject firstEntry = result.getJSONObject(0);
        assertEquals(1, firstEntry.getInt("id"));
        assertEquals("John", firstEntry.getString("name"));
        assertEquals(30, firstEntry.getInt("age"));
    }

    @Test
    void testConvertCsvToJson_ValidCsv_ReturnsJson() throws IOException {
        File csvFile = new File(tempFileLocation, "test.csv");
        try (FileWriter writer = new FileWriter(csvFile)) {
            writer.write("Name,Age,City\nJohn,25,New York\n");
        }

        JsonNode result = s3FileUploadService.convertCsvToJson(csvFile.getAbsolutePath());

        assertNotNull(result);
        assertEquals(1, result.size());

        assertEquals("John", result.get(0).get("Name").asText());
        assertEquals("25", result.get(0).get("Age").asText());
        assertEquals("New York", result.get(0).get("City").asText());
    }

    @Test
    void testConvertCsvToJson_NoValidDelimiter_ThrowsException() {
        File csvFile = new File(tempFileLocation, "invalid.csv");
        try (FileWriter writer = new FileWriter(csvFile)) {
            writer.write("InvalidHeader1 InvalidHeader2 InvalidHeader3\n"); // No valid delimiter
        } catch (IOException e) {
            fail("Test setup failed: " + e.getMessage());
        }

        Exception exception = assertThrows(IllegalArgumentException.class,
                () -> s3FileUploadService.convertCsvToJson(csvFile.getAbsolutePath()));

        assertEquals("No valid delimiter found in the CSV file.", exception.getMessage());
    }

    @SuppressWarnings("deprecation")
    @Test
    void testUploadDatabaseFileToS3Bucket_Success() throws Exception {
        String filePath = "test-folder";
        String fileName = "test.db";
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        String s3FileName = request.organizationPartyId() + "/" + request.studyId() + "/Database/" + fileName;
        URL mockUrl = new URL("https://mock-s3-url.com/" + s3FileName);

        when(s3Client.utilities()).thenReturn(s3Utilities);
        when(s3Utilities.getUrl(any(GetUrlRequest.class))).thenReturn(mockUrl);

        String result = s3FileUploadService.uploadDatabaseFileToS3Bucket(filePath, fileName, request);

        assertNotNull(result);
        assertEquals(mockUrl.toString(), result);
        verify(s3Client).putObject(any(PutObjectRequest.class), eq(Paths.get(filePath + "/" + fileName)));
    }

    @Test
    void testUploadDatabaseFileToS3Bucket_GeneralException() {
        String filePath = "test-folder";
        String fileName = "test.db";
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        // Simulate a general exception being thrown during S3 upload
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenThrow(new RuntimeException("Unexpected error occurred"));

        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> s3FileUploadService.uploadDatabaseFileToS3Bucket(filePath, fileName, request));

        assertTrue(exception.getMessage().contains("Error uploading file to S3"));
    }

    @SuppressWarnings("deprecation")
    @Test
    void testUploadDBFileToS3_Success() throws IOException {
        MultipartFile mockFile = mock(MultipartFile.class);
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        when(mockFile.getOriginalFilename()).thenReturn("test.db");
        when(mockFile.getSize()).thenReturn(1024L);

        // Provide a correct SQLite file signature
        byte[] validSQLiteHeader = "SQLite format 3\0".getBytes(StandardCharsets.US_ASCII);
        ByteArrayInputStream validSQLiteStream = new ByteArrayInputStream(validSQLiteHeader);
        when(mockFile.getInputStream()).thenReturn(validSQLiteStream);

        // Mocking S3Utilities
        S3Utilities mockUtilities = mock(S3Utilities.class);
        when(s3Client.utilities()).thenReturn(mockUtilities);

        // Mock URL response
        URL mockUrl = new URL("https://mock-s3-url.com/test.db");
        when(mockUtilities.getUrl(any(GetUrlRequest.class))).thenReturn(mockUrl);

        String result = s3FileUploadService.uploadDBFileToS3(mockFile, request);

        assertNotNull(result);
        assertEquals("https://mock-s3-url.com/test.db", result);
    }

    @Test
    void testUploadDBFileToS3_InvalidSQLiteFile_ThrowsException() throws IOException {
        MultipartFile mockFile = mock(MultipartFile.class);
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        when(mockFile.getOriginalFilename()).thenReturn("test.db");
        when(mockFile.getInputStream())
                .thenReturn(new ByteArrayInputStream("InvalidHeaderData".getBytes(StandardCharsets.US_ASCII)));

        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> s3FileUploadService.uploadDBFileToS3(mockFile, request));

        assertTrue(exception.getMessage().contains("Invalid database file. Please upload a valid SQLite database."));
    }

    @Test
    void testUploadDBFileToS3_GeneralExceptionHandling() throws IOException {
        MultipartFile mockFile = mock(MultipartFile.class);
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        when(mockFile.getOriginalFilename()).thenReturn("test.db");
        when(mockFile.getInputStream()).thenThrow(new IOException("Simulated IOException"));

        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> s3FileUploadService.uploadDBFileToS3(mockFile, request));

        System.out.println("Actual Exception Message: " + exception.getMessage()); // Debugging

        assertTrue(exception.getMessage().contains("Error uploading file to S3"));
    }

    @Test
    void testUploadDBFileToS3_SandboxMode_ReturnsDummyUrl() {
        ReflectionTestUtils.setField(s3FileUploadService, "isSandbox", true);

        MultipartFile mockFile = mock(MultipartFile.class);
        DatabaseMigrationRequest request = new DatabaseMigrationRequest("org123", "study456");

        String result = s3FileUploadService.uploadDBFileToS3(mockFile, request);

        assertEquals("dummyUrl", result);
    }

    @Test
    void testParseTextToJSONArray_ValidKeyValueFormat() throws Exception {
        String content = "name: John\nage: 30\ncity: New York\n";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        Method parseTextToJSONArrayMethod = S3FileUploadService.class.getDeclaredMethod("parseTextToJSONArray",
                byte[].class);
        parseTextToJSONArrayMethod.setAccessible(true);
        JSONArray result = (JSONArray) parseTextToJSONArrayMethod.invoke(s3FileUploadService, fileBytes);

        assertNotNull(result);
        assertEquals(3, result.length());

        JSONObject firstObject = result.getJSONObject(0);
        assertEquals("John", firstObject.getString("name"));

        JSONObject secondObject = result.getJSONObject(1);
        assertEquals("30", secondObject.getString("age"));

        JSONObject thirdObject = result.getJSONObject(2);
        assertEquals("New York", thirdObject.getString("city"));
    }

    @Test
    void testParseTextToJSONArray_EmptyContent() throws Exception {
        String content = "";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        Method parseTextToJSONArrayMethod = S3FileUploadService.class.getDeclaredMethod("parseTextToJSONArray",
                byte[].class);
        parseTextToJSONArrayMethod.setAccessible(true);
        JSONArray result = (JSONArray) parseTextToJSONArrayMethod.invoke(s3FileUploadService, fileBytes);

        assertNotNull(result);
        assertEquals(0, result.length());
    }

    @Test
    void testParseTextToJSONArray_InvalidFormat() throws Exception {
        String content = "invalid line without colon\nanother invalid line\n";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        Method parseTextToJSONArrayMethod = S3FileUploadService.class.getDeclaredMethod("parseTextToJSONArray",
                byte[].class);
        parseTextToJSONArrayMethod.setAccessible(true);
        JSONArray result = (JSONArray) parseTextToJSONArrayMethod.invoke(s3FileUploadService, fileBytes);

        assertNotNull(result);
        assertEquals(0, result.length());
    }

    @Test
    void testParseTextToJSONArray_MixedValidInvalidLines() throws Exception {
        String content = "name: John\ninvalid line\nage: 30\n";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        Method parseTextToJSONArrayMethod = S3FileUploadService.class.getDeclaredMethod("parseTextToJSONArray",
                byte[].class);
        parseTextToJSONArrayMethod.setAccessible(true);
        JSONArray result = (JSONArray) parseTextToJSONArrayMethod.invoke(s3FileUploadService, fileBytes);

        assertNotNull(result);
        assertEquals(2, result.length());

        JSONObject firstObject = result.getJSONObject(0);
        assertEquals("John", firstObject.getString("name"));

        JSONObject secondObject = result.getJSONObject(1);
        assertEquals("30", secondObject.getString("age"));
    }

    @Test
    void testParseTextToJSONArray_WithWhitespace() throws Exception {
        String content = "  name  :  John  \n  age  :  30  \n";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);

        Method parseTextToJSONArrayMethod = S3FileUploadService.class.getDeclaredMethod("parseTextToJSONArray",
                byte[].class);
        parseTextToJSONArrayMethod.setAccessible(true);
        JSONArray result = (JSONArray) parseTextToJSONArrayMethod.invoke(s3FileUploadService, fileBytes);

        assertNotNull(result);
        assertEquals(2, result.length());

        JSONObject firstObject = result.getJSONObject(0);
        assertEquals("John", firstObject.getString("name"));

        JSONObject secondObject = result.getJSONObject(1);
        assertEquals("30", secondObject.getString("age"));
    }

    @Test
    void testParseLineWithQuotes_SimpleCommaDelimited() throws Exception {
        String line = "John,30,New York";
        char delimiter = ',';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("John", result.get(0));
        assertEquals("30", result.get(1));
        assertEquals("New York", result.get(2));
    }

    @Test
    void testParseLineWithQuotes_WithQuotedValues() throws Exception {
        String line = "\"John Doe\",30,\"New York, NY\"";
        char delimiter = ',';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("John Doe", result.get(0));
        assertEquals("30", result.get(1));
        assertEquals("New York, NY", result.get(2));
    }

    @Test
    void testParseLineWithQuotes_EmptyLine() throws Exception {
        String line = "";
        char delimiter = ',';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
    }

    @Test
    void testParseLineWithQuotes_SingleValue() throws Exception {
        String line = "John";
        char delimiter = ',';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("John", result.get(0));
    }

    @Test
    void testParseLineWithQuotes_TabDelimited() throws Exception {
        String line = "John\t30\tNew York";
        char delimiter = '\t';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("John", result.get(0));
        assertEquals("30", result.get(1));
        assertEquals("New York", result.get(2));
    }

    @Test
    void testParseLineWithQuotes_WithEmptyFields() throws Exception {
        String line = "John,,New York";
        char delimiter = ',';

        Method parseLineWithQuotesMethod = S3FileUploadService.class.getDeclaredMethod("parseLineWithQuotes",
                String.class, char.class);
        parseLineWithQuotesMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) parseLineWithQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("John", result.get(0));
        assertEquals("", result.get(1));
        assertEquals("New York", result.get(2));
    }

    @Test
    void testCountDelimitersOutsideQuotes_NoQuotes() throws Exception {
        String line = "John,30,New York";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(2, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_WithQuotes() throws Exception {
        String line = "\"John, Jr.\",30,\"New York, NY\"";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(2, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_EmptyString() throws Exception {
        String line = "";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(0, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_NoDelimiters() throws Exception {
        String line = "John Doe";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(0, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_OnlyQuotedContent() throws Exception {
        String line = "\"John, Smith, Jr.\"";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(0, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_TabDelimiter() throws Exception {
        String line = "John\t30\t\"New\tYork\"";
        char delimiter = '\t';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(2, result);
    }

    @Test
    void testCountDelimitersOutsideQuotes_MultipleQuotePairs() throws Exception {
        String line = "\"John, Jr.\",\"Age: 30\",\"City: New York, NY\"";
        char delimiter = ',';

        Method countDelimitersOutsideQuotesMethod = S3FileUploadService.class
                .getDeclaredMethod("countDelimitersOutsideQuotes", String.class, char.class);
        countDelimitersOutsideQuotesMethod.setAccessible(true);
        int result = (Integer) countDelimitersOutsideQuotesMethod.invoke(s3FileUploadService, line, delimiter);

        assertEquals(2, result);
    }

}
