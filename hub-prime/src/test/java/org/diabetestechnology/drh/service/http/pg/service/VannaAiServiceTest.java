package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI.VannaAIRequest;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;

import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
public class VannaAiServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private ResultQuery<Record1<JSONB>> queryMock;

    @InjectMocks
    private VannaAiService vannaAiService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_Success() throws Exception {
        // Arrange
        String question = "What is the total number of patients?";
        String sql = "SELECT COUNT(*) FROM patients";
        String result = "150";
        String userPartyId = "user123";

        VannaAIRequest request = new VannaAIRequest(question, sql, result);
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"12345\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenReturn(userPartyId);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        // Inject mocked DSLContext and UserNameService using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act
        Object actualResult = vannaAiService.saveVannaRequestAndResponse(request);

        // Assert
        assertNotNull(actualResult);
        assertEquals(expectedResult, actualResult);

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_WithNullResult() throws Exception {
        // Arrange
        String question = "What is the average age?";
        String sql = "SELECT AVG(age) FROM patients";
        String userPartyId = "user456";

        VannaAIRequest request = new VannaAIRequest(question, sql, null);
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"67890\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenReturn(userPartyId);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        // Inject mocks using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act
        Object actualResult = vannaAiService.saveVannaRequestAndResponse(request);

        // Assert
        assertNotNull(actualResult);
        assertEquals(expectedResult, actualResult);

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_WithCompleteRequest() throws Exception {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        VannaAIRequest request = new VannaAIRequest(
                1L,
                "How many studies are active?",
                "SELECT COUNT(*) FROM studies WHERE status = 'active'",
                "25",
                timestamp);
        String userPartyId = "user789";
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"abc123\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenReturn(userPartyId);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        // Inject mocks using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act
        Object actualResult = vannaAiService.saveVannaRequestAndResponse(request);

        // Assert
        assertNotNull(actualResult);
        assertEquals(expectedResult, actualResult);

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_DatabaseException() throws Exception {
        // Arrange
        VannaAIRequest request = new VannaAIRequest("Test question", "SELECT 1", "1");
        String userPartyId = "user999";

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenReturn(userPartyId);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("Database connection failed"));

        // Inject mocks using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            vannaAiService.saveVannaRequestAndResponse(request);
        });

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_UserServiceException() throws Exception {
        // Arrange
        VannaAIRequest request = new VannaAIRequest("Test question", "SELECT 1", "1");

        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenThrow(new RuntimeException("User service unavailable"));

        // Inject mocks using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            vannaAiService.saveVannaRequestAndResponse(request);
        });

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, never()).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveVannaRequestAndResponse_EmptyQuestion() throws Exception {
        // Arrange
        VannaAIRequest request = new VannaAIRequest("", "SELECT 1", "1");
        String userPartyId = "user111";
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"empty123\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        DSLContext dslMock = mock(DSLContext.class);

        when(userNameService.getCurrentuserPartyId()).thenReturn(userPartyId);
        when(dslMock.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        // Inject mocks using reflection
        java.lang.reflect.Field dslField = VannaAiService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(vannaAiService, dslMock);

        java.lang.reflect.Field userNameServiceField = VannaAiService.class.getDeclaredField("userNameService");
        userNameServiceField.setAccessible(true);
        userNameServiceField.set(vannaAiService, userNameService);

        // Act
        Object actualResult = vannaAiService.saveVannaRequestAndResponse(request);

        // Assert
        assertNotNull(actualResult);
        assertEquals(expectedResult, actualResult);

        verify(userNameService, times(1)).getCurrentuserPartyId();
        verify(dslMock, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).fetchOneInto(JSONB.class);
    }
}
