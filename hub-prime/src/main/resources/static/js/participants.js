let editActive = false;
let studyArchived = false;
const params = new URLSearchParams(window.location.search);
const tab = params.get("tab");
document.addEventListener("DOMContentLoaded", async function () {
  let studyId = "";
  let fileName = "";
  let tableName = "";
  const scriptTag = document.querySelector('script[src="/js/participants.js"]');
  if (scriptTag) {
    studyId = scriptTag.getAttribute("data-studyid");
    fileName = scriptTag.getAttribute("data-fileName");
    tableName = scriptTag.getAttribute("data-tablename");
  }
  const svgIcons = {
    dashboard: `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 6.66667H5.33333V0H0V6.66667ZM0 12H5.33333V8H0V12ZM6.66667 12H12V5.33333H6.66667V12ZM6.66667 0V4H12V0H6.66667Z" fill="#737373" />
</svg>`,
    cgmdata: `<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7368 11.4545H3.78947C3.44211 11.4545 3.14484 11.33 2.89768 11.081C2.65011 10.8315 2.52632 10.5318 2.52632 10.1818V1.27273C2.52632 0.922727 2.65011 0.623 2.89768 0.373545C3.14484 0.124515 3.44211 0 3.78947 0H7.68947C7.85789 0 8.01853 0.0318182 8.17137 0.0954545C8.32379 0.159091 8.45789 0.249242 8.57368 0.365909L11.6368 3.45227C11.7526 3.56894 11.8421 3.70406 11.9053 3.85764C11.9684 4.01164 12 4.17348 12 4.34318V10.1818C12 10.5318 11.8764 10.8315 11.6293 11.081C11.3817 11.33 11.0842 11.4545 10.7368 11.4545ZM1.26316 14C0.915789 14 0.618526 13.8755 0.371368 13.6265C0.12379 13.377 0 13.0773 0 12.7273V4.45455C0 4.27424 0.0606317 4.123 0.181895 4.00082C0.302737 3.87906 0.452632 3.81818 0.631579 3.81818C0.810526 3.81818 0.960632 3.87906 1.08189 4.00082C1.20274 4.123 1.26316 4.27424 1.26316 4.45455V12.7273H7.57895C7.75789 12.7273 7.908 12.7884 8.02926 12.9105C8.1501 13.0323 8.21053 13.1833 8.21053 13.3636C8.21053 13.5439 8.1501 13.695 8.02926 13.8167C7.908 13.9389 7.75789 14 7.57895 14H1.26316ZM8.21053 4.45455H10.7368L7.57895 1.27273V3.81818C7.57895 3.99848 7.63958 4.14973 7.76084 4.27191C7.88168 4.39367 8.03158 4.45455 8.21053 4.45455Z" fill="#737373"/>
</svg>`,
    mealsdata: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-4">
  <path fill-rule="evenodd" d="M17.663 3.118c.**************.673.05C19.876 3.298 21 4.604 21 6.109v9.642a3 3 0 0 1-3 3V16.5c0-5.922-4.576-10.775-10.384-11.217.324-1.132 1.3-2.01 2.548-2.114.224-.019.448-.036.673-.051A3 3 0 0 1 13.5 1.5H15a3 3 0 0 1 2.663 1.618ZM12 4.5A1.5 1.5 0 0 1 13.5 3H15a1.5 1.5 0 0 1 1.5 1.5H12Z" clip-rule="evenodd" />
  <path d="M3 8.625c0-1.036.84-1.875 1.875-1.875h.375A3.75 3.75 0 0 1 9 10.5v1.875c0 1.036.84 1.875 1.875 1.875h1.875A3.75 3.75 0 0 1 16.5 18v2.625c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625v-12Z" />
  <path d="M10.5 10.5a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963 5.23 5.23 0 0 0-3.434-1.279h-1.875a.375.375 0 0 1-.375-.375V10.5Z" />
</svg>
`,
    fitnessdata: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-4">
  <path fill-rule="evenodd" d="M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z" clip-rule="evenodd" />
  <path fill-rule="evenodd" d="M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375ZM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
</svg>
`,
  };
  // Check if the pageTabs element exists
  const pageTabs = document.getElementById("pageTabs");
  if (pageTabs) {
    const tabs = [
      {
        text: "Dashboard",
        key: "dashboard",
        href:
          "/participants/info/" + studyId + "/" + participantId + "?tab=" + tab,
      },
      {
        text: "CGM Data",
        key: "cgmdata",
        href:
          "/participants/cgmdata/" +
          studyId +
          "/" +
          participantId +
          "?tab=" +
          tab,
      },
      {
        text: "Meals Data",
        key: "mealsdata",
        href:
          "/participants/mealsdata/" +
          studyId +
          "/" +
          participantId +
          "?tab=" +
          tab,
      },
      {
        text: "Fitness Data",
        key: "fitnessdata",
        href:
          "/participants/fitnessdata/" +
          studyId +
          "/" +
          participantId +
          "?tab=" +
          tab,
      },
    ];

    // Get the active route path dynamically (replace with your logic)
    let pageSegment = window.location.pathname.split("/")[2];
    const activeRouteKey = window.location.pathname.includes(
      "participants/info"
    )
      ? "dashboard"
      : pageSegment;

    // Helper function to create an SVG wrapper
    const createSvgWrapper = (svgHTML) => {
      const iconWrapper = document.createElement("div");
      iconWrapper.innerHTML = svgHTML;
      iconWrapper.classList.add("mr-2");
      return iconWrapper;
    };

    // Populate tabs only if empty
    if (pageTabs.innerHTML.trim() === "") {
      tabs.forEach((tab) => {
        const li = document.createElement("li");
        li.className = "mr-2 hidden";
        if (tab.text == "Dashboard") {
          li.setAttribute("data-privilege-tabs", "View Metrics");
        } else {
          li.setAttribute("data-privilege-tabs", tab.text);
        }
        const a = document.createElement("a");
        a.href = tab.href;
        a.textContent = tab.text;
        a.className =
          tab.key === activeRouteKey
            ? "flex items-center bg-white border border-b-0 text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center mb-[-1px] active"
            : "flex items-center text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center hover:text-gray-600 hover:bg-gray-50";

        // Add SVG if available
        const svgHTML = svgIcons[tab.key];
        if (svgHTML) {
          a.prepend(createSvgWrapper(svgHTML));
        }

        li.appendChild(a);
        pageTabs.appendChild(li);
      });
      hideUnauthorizedTabs();
    }
  } else {
    console.error("#pageTabs element not found.");
  }
  if (studyId) {
    let studyOwner = await getStudyOwner(studyId);

    console.log("studyOwner", studyOwner);
    console.log("studyOwner", studyOwner);
    if (studyOwner != null && studyOwner != getLoggedInUser()) {
      toggleTooltip("edit-participant-button", true);
      toggleTooltip("cgm-upload-container", true);
      document.getElementById("meals-upload-form")?.classList.add("hidden");
      document.getElementById("fitness-upload-form")?.classList.add("hidden");
    } else if (studyOwner == getLoggedInUser()) {
      let archiveStatus = await getStudyArchiveStatus(studyId);

      console.log("archiveStatus", archiveStatus);
      console.log("participant js", archiveStatus);

      if (archiveStatus == true) {
        toggleTooltip("edit-participant-button", true);
        toggleTooltip("cgm-upload-container", true);
        studyArchived = true;
        document.getElementById("meals-upload-form")?.classList.add("hidden");
        document.getElementById("fitness-upload-form")?.classList.add("hidden");
      } else {
         let privilegeListArray = [];
          privilegeListArray =
      JSON.parse(localStorage.getItem("userPrivilegeInfoArray")) || "[]";
      if (privilegeListArray.includes("Edit Meals Data")) {
        document
          .getElementById("meals-upload-form")
          ?.classList.remove("hidden");
      }
      if (privilegeListArray.includes("Edit Fitness Data")) {
        document
          .getElementById("fitness-upload-form")
          ?.classList.remove("hidden");
      }
        document.querySelectorAll(".participant-item").forEach((element) => {
          element.addEventListener("mouseenter", function (event) {
            if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
              const clickedElement = element.children[0].children[0];
              if (!editActive || editActive == false)
                clickedElement.classList.remove("hidden");
            }
          });
          element.addEventListener("mouseleave", function (event) {
            if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
              const clickedElement = element.children[0].children[0];
              clickedElement.classList.add("hidden");
            }
          });
        });

        document.querySelectorAll(".study-edit-field").forEach((element) => {
          element.addEventListener("click", function (event) {
            const clickedElement = element;
            const placeholderText = clickedElement.innerText;
            const elementId = clickedElement.getAttribute("data-id");
            let elementType = clickedElement.getAttribute("data-type");
            let fieldValue = document.getElementById(elementId).textContent;

            // Hide the clicked element and show the input field
            clickedElement.classList.add("hidden");
            document.getElementById(elementId).classList.add("hidden");
            document.getElementById("add" + elementId)?.classList.add("hidden");
            // if (elementType == "dropdown") {
            if (elementId == "ethnicity_type_id") {
              getEthnicityList()
                .then((data) => {
                  createOptions(elementId + "Dropdown", data);
                })
                .catch((error) => {
                  console.error("Error fetching data:", error);
                });
            }
            if (elementId == "gender_type_id") {
              getGenderList()
                .then((data) => {
                  createOptions(elementId + "Dropdown", data);
                })
                .catch((error) => {
                  console.error("Error fetching data:", error);
                });
            }
            if (elementId == "race_type_id") {
              getRaceList()
                .then((data) => {
                  createOptions(elementId + "Dropdown", data);
                })
                .catch((error) => {
                  console.error("Error fetching data:", error);
                });
            }
            // }
            // inputField.value = ''; // Pre-fill the input with existing text
            const inputField = document.getElementById(
              elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
            );
            inputField.parentElement.classList.remove("hidden");
            if (elementId == "bmi") {
              fieldValue = fieldValue.replace(" kg/m²", "");
            }
            if (elementId == "baselineHba1c") {
              fieldValue = fieldValue.replace(" %", "");
            }
            if (elementId == "age") {
              fieldValue = fieldValue.replace(" Years", "");
            }
            inputField.value = fieldValue || "";
            inputField.focus();
            editActive = true;
          });
        });

        toggleTooltip("edit-participant-button", false);
        toggleTooltip("cgm-upload-container", false);
        studyArchived = false;
      }
    }
  }
  if (document.getElementById("race")) {
    getRaceList()
      .then((data) => {
        createOptions("race", data);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
      });
  }
  if (document.getElementById("gender")) {
    getGenderList()
      .then((data) => {
        createOptions("gender", data);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
      });
  }
  if (document.getElementById("ethnicity")) {
    getEthnicityList()
      .then((data) => {
        createOptions("ethnicity", data);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
      });
  }

  getParticipantInfo();

  document.querySelectorAll(".save-edit-field").forEach((element) => {
    element.addEventListener("click", async function (event) {
      const clickedElement = element;
      const elementId = clickedElement.getAttribute("data-id");
      let elementType = clickedElement.getAttribute("data-type");

      const inputField = document.getElementById(
        elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
      );
      const newValue = DOMPurify.sanitize(inputField.value.trim());
      if (elementId == "gender_type_id" && newValue == "") {
        showToast("Unable to delete the gender", "error");
        hideLoading(elementId + "Loader");
        return;
      }
      if (elementId == "age" && newValue == "") {
        showToast("Unable to delete the age", "error");
        hideLoading(elementId + "Loader");
        return;
      }

      // if (newValue !== '') {
      showLoading(elementId + "Loader");
      if (elementId == "age" && (newValue < 0 || newValue > 150)) {
        showToast("Age should be between 0 and 150", "error");
        hideLoader(elementId + "Loader");
        return;
      }
      try {
        // Send the user's message to the API
        const response = await fetch(`/participant/${participantId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            [elementId]: newValue,
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to fetch response from the API.");
        }

        const data = await response.json();
        if (data.status === "success") {
          showToast("Participant updated successfully!", "success");
          //  assignValues(elementId , newValue);
          if (
            elementId == "gender_type_id" ||
            elementId == "race_type_id" ||
            elementId == "ethnicity_type_id"
          ) {
            document.getElementById(elementId).innerText =
              inputField.selectedOptions[0].textContent;
            assignValues(elementId, inputField.selectedOptions[0].textContent);
          } else {
            document.getElementById(elementId).innerText = newValue;
            assignValues(elementId, newValue);
          }
          // Hide input field, show the original element
          if (newValue == "") {
            document
              .getElementById("add" + elementId)
              .classList.remove("hidden");
            document.getElementById(elementId).classList.add("hidden");
          } else {
            document.getElementById(elementId).classList.remove("hidden");
            document.getElementById("add" + elementId).classList.add("hidden");
          }

          inputField.parentElement.classList.add("hidden");
        } else {
          showToast("Something went wrong!", "error");
        }
      } catch (e) {
        console.error("Error fetching API response:", e);
      }
      editActive = false;
      hideLoading(elementId + "Loader");
    });
  });

  document.querySelectorAll(".cancel-edit-field").forEach((element) => {
    element.addEventListener("click", function (event) {
      const clickedElement = element;
      const elementId = clickedElement.getAttribute("data-id");

      let elementType = clickedElement.getAttribute("data-type");

      const inputField = document.getElementById(
        elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
      );

      clickedElement.parentElement?.classList?.remove("hidden");

      let fieldValue = document.getElementById(elementId).textContent;
      if (!fieldValue || fieldValue == "") {
        document.getElementById("add" + elementId).classList.remove("hidden");
      } else {
        document.getElementById(elementId)?.classList?.remove("hidden");
      }
      // Hide input field, show the original element
      if (elementType == "dropdown") {
        document
          .getElementById(elementId + "Dropdown")
          .parentElement.classList.add("hidden");
      } else {
        inputField.parentElement.classList.add("hidden");
      }
      editActive = false;
    });
  });
  const editParticipant = document.getElementById("edit-participant-button");

  editParticipant?.addEventListener("click", function () {
    window.location =
      "/participants/settings/" + studyId + "/" + participantId + "?tab=" + tab;
  });
});

function saveParticipant() {
  let currentPage = document.referrer.includes("all") ? "all" : "mystudies";
  window.location =
    "/participants/info/" + studyId + "/" + participantId + "?tab=" + tab;
}

function getParticipantInfo() {
  fetchData(`/participant/${participantId}`, (data, error) => {
    if (!error) {
      const result = data.data.participantData;

      result.participant_display_id != null
        ? assignValues("participantId", result.participant_display_id)
        : "";
      result.participant_age != null
        ? assignValues("age", result.participant_age)
        : "";
      result.bmi != null ? assignValues("bmi", result.bmi) : "";
      result.baseline_hba1c != null
        ? assignValues("baseline_hba1c", result.baseline_hba1c)
        : "";
      result.diabetes_type != null
        ? assignValues("diabetes_type", result.diabetes_type)
        : "";
      result.study_arm != null
        ? assignValues("study_arm", result.study_arm)
        : "";
      result.participant_race != null
        ? assignValues("race_type_id", result.participant_race)
        : "";
      result.participant_ethnicity != null
        ? assignValues("ethnicity_type_id", result.participant_ethnicity)
        : "";
      result.treatment_modality != null
        ? assignValues("treatment_modality", result.treatment_modality)
        : "";
      result.diagnosis_icd != null
        ? assignValues("diagnosis_icd", result.diagnosis_icd)
        : "";
      result.med_rxnorm != null
        ? assignValues("med_rxnorm", result.med_rxnorm)
        : "";
      result.participant_gender != null
        ? assignValues("gender_type_id", result.participant_gender)
        : "";
    } else {
      console.error("Error fetching participant info:", error);
    }
  });
}

function assignValues(eleClass, value) {
  document.querySelectorAll("." + eleClass).forEach((element) => {
    element.classList.remove("hidden");
    element.innerHTML = value;
    let elementId = element.getAttribute("data-id");
    if (
      elementId == "gender_type_id" ||
      elementId == "race_type_id" ||
      elementId == "ethnicity_type_id"
    ) {
      let selectBox = document.getElementById(elementId + "Dropdown");
      selectBox.value = value;
    }
    if (value !== "") {
      hidePlaceholders(eleClass);
      appendMeasures(eleClass, element);
    }
  });
}

function hidePlaceholders(eleClass) {
  document.getElementById("add" + eleClass)?.classList?.add("hidden");
}
function appendMeasures(eleClass, element) {
  if (eleClass == "bmi") {
    element.innerHTML += " kg/m²";
  }
  if (eleClass == "baselineHba1c") {
    element.innerHTML += " %";
  }
  if (eleClass == "age") {
    element.innerHTML += " Years";
  }
}

function showParticipantDisplayLoader() {
  document
    .getElementById("participantDisplayId-loader")
    .classList.remove("hidden");
}

function hideParticipantDisplayLoader() {
  document
    .getElementById("participantDisplayId-loader")
    .classList.add("hidden");
}
