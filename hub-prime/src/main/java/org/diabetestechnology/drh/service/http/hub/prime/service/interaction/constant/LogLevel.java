package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

public class LogLevel {

    // ERROR_LOG_LEVELS
    public static final int ERROR = 0;
    // LEVEL 1- PRIMARY_MENU
    public static final int SKIP_LOGIN = 1;
    public static final int ORCID_LOGIN = 1;
    public static final int GIT_LOGIN = 1;
    public static final int HOME = 1;
    public static final int STUDIES = 1;
    public static final int COHORT = 1;
    public static final int ASK_DRH = 1;
    public static final int CONSOLE = 1;
    public static final int DOCUMENTATION = 1;
    public static final int PROFILE = 1;

    // LEVEL 2- SECONDARY_MENU
    // STUDIES
    public static final int DASHBOARD = 2;
    public static final int ALL_STUDIES = 2;
    public static final int POPULATION_PERCENTAGE = 2;
    public static final int MY_STUDIES = 2;
    // ADK_DRH
    public static final int ASK_DRH_DATA = 2;
    public static final int ASK_DRH_RESEARCH_JOURNAL = 2;
    public static final int ASK_DRH_ICODE = 2;
    // CONSOLE
    public static final int CONSOLE_PROJECT = 2;
    public static final int CONSOLE_HEALTH_INFORMATION = 2;
    public static final int CONSOLE_SCHEMA = 2;
    // DOCUMENTATION
    public static final int DOCUMENTATION_OPEN_API = 2;
    public static final int DOCUMENTATION_ANNOUNCEMENTS = 2;

    // LEVEL 3- TERNARY_APPLICATION_LEVEL
    public static final int STUDIES_INDIVIDUAL = 3;
    public static final int STUDIES_PARTICIPANT = 3;
    public static final int ALL_STUDIES_CGM = 3;
    public static final int STUDIES_CGM = 3;

    public static final int SUCCESSFUL_STUDY_INTERACTION = 3;
    public static final int FAILED_STUDY_INTERACTION = 3;
    public static final int SUCCESSFUL_PARTICIPANT_INTERACTION = 3;
    public static final int FAILED_PARTICIPANT_INTERACTION = 3;
    public static final int DATABASE_INTERACTION = 3;
    public static final int SUCCESSFUL_PARTICIPANT_FILE_INTERACTION = 3;
    public static final int FAILED_PARTICIPANT_FILE_INTERACTION = 3;
    public static final int SUCCESSFUL_CGM_FILE_INTERACTION = 3;
    public static final int FAILED_CGM_FILE_INTERACTION = 3;
    public static final int SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION = 3;
    public static final int FAILED_MEALS_OR_FITNESS_INTERACTION = 3;
    public static final int USER_SESSION = 3;

    // LEVEL 4- QUATERNARY_APPLICATION_LEVEL
    public static final int AI_ASK_DRH_DATA = 4;
    public static final int AI_ASK_DRH_RESEARCH_JOURNAL = 4;
    public static final int AI_ASK_DRH_ICODE = 4;

    public static final int UPLOAD_STUDY_DATABASE = 4;
    public static final int STUDY_SETTINGS = 4;
    public static final int COLLABORATION_AND_TEAMS = 4;
    public static final int PUBLICATION_SETTINGS = 4;
    public static final int ADD_PARTICIPANT = 4;
    public static final int EDIT_PARTICIPANT = 4;
    public static final int PARTICIPANT_CGM_DATA = 4;
    public static final int PARTICIPANT_MEALS_DATA = 4;
    public static final int PARTICIPANT_FITNESS_DATA = 4;

    // LEVEL 5- QUINARY_APPLICATION_LEVEL
    public static final int LOGIN = 5;
    public static final int ACTIVITY_LOG = 5;

    // LEVEL 6- LESS_PRIORITY_LOG_LEVELS
    public static final int DEFAULT = 6;
    public static final int SAVE_ACTIVITY_LOG = 6;
}
